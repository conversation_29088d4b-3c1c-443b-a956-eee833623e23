# Collection Meta Generator

This script generates optimized meta titles and meta descriptions for your Shopify collections using GPT-4.

## How It Works

1. The script offers three ways to input collections:
   - Read from your sitemap.txt file
   - Enter collection names manually
   - Process specific collections by name
2. It uses GPT-4 to generate SEO-optimized meta titles and descriptions for each collection
3. Results are displayed in the console and saved to a CSV file

## Requirements

- Python 3.6+
- Required packages: g4f, tqdm, tenacity

## Installation

Install the required packages:

```bash
pip install g4f tqdm tenacity
```

## Usage

1. Run the script:
   ```bash
   python collection_meta.py
   ```

2. Choose how you want to input collections:
   - Option 1: Read from sitemap.txt (automatically extracts collection names from URLs)
   - Option 2: Enter collection names and descriptions manually
   - Option 3: Enter specific collection names to process

3. The script will process each collection and generate meta titles and descriptions.

4. Results will be:
   - Displayed in the console as they are generated
   - Saved to `collections_meta.csv` with the format:
     ```
     Collection Name,Description,Meta Title,Meta Description
     ```
   - Shown in a copy-paste friendly format at the end

## Input Methods

### Option 1: Read from sitemap.txt

The script will look for a sitemap.txt file in the `../site_console/` directory and extract collection names from URLs that contain `/collections/`.

### Option 2: Enter manually

You'll be prompted to enter collection names and descriptions one by one. Press Enter with an empty name to finish.

### Option 3: Process specific collections

Enter just the collection names you want to process, one per line. Press Enter with an empty line to finish.

## Customization

You can modify the system prompt in the script to adjust the guidelines for generating meta titles and descriptions. Look for the system content in the `generate_meta_data` function.

## Troubleshooting

- If you encounter errors with the g4f library, make sure you're using the latest version.
- If the script fails to parse the JSON response, it will attempt to extract the data using string parsing.
- Check the console output for any error messages during execution.

## Notes

- The script uses threading to process multiple collections simultaneously, which can be adjusted by changing the `max_workers` parameter in the `ThreadPoolExecutor`.
- The retry mechanism will attempt to generate meta data up to 5 times if there are failures.
