import json
import os
import requests
import time
import sys
from datetime import datetime, timedelta, timezone

# Add parent directory to path to import config
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Load the JSON config file
def load_config():
    config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'config.json')
    with open(config_path, 'r') as config_file:
        return json.load(config_file)

config = load_config()

# Shopify API credentials
password = config['shopify']['password']
store_address = config['shopify']['store_address']
PRODUCT_ID_FILE_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), config['csv']['product_id_txt'])

# Function to read the product ID from the product ID file
def read_product_id(file_path):
    with open(file_path, 'r') as file:
        product_id = file.read().strip()
    return product_id

# Function to fetch collections created in the last 24 hours
def fetch_recent_collections(hours=24, max_retries=5):
    """
    Fetch collections created in the last specified hours (default: 24 hours)
    This function fetches ALL collections and filters them client-side for recent ones
    """
    print(f"🔄 Fetching all collections to filter for recent ones...")

    # Calculate the datetime cutoff
    cutoff_time = datetime.now(timezone.utc) - timedelta(hours=hours)
    print(f"🕒 Looking for collections created after: {cutoff_time.strftime('%Y-%m-%d %H:%M:%S UTC')}")

    # Use the existing function to fetch all collections
    all_collections = fetch_all_collections(max_retries)

    if not all_collections:
        print("❌ Failed to fetch collections")
        return []

    print(f"📊 Total collections fetched: {len(all_collections)}")

    # Filter for recent collections
    recent_collections = []
    collections_with_dates = 0

    for collection in all_collections:
        # Try created_at first, then published_at as fallback
        date_field = None
        date_value = None

        if 'created_at' in collection and collection['created_at']:
            date_field = 'created_at'
            date_value = collection['created_at']
        elif 'published_at' in collection and collection['published_at']:
            date_field = 'published_at'
            date_value = collection['published_at']

        if date_value:
            collections_with_dates += 1
            try:
                # Parse the timestamp
                date_str = date_value
                # Handle different timestamp formats
                if date_str.endswith('Z'):
                    date_str = date_str[:-1] + '+00:00'
                elif '+' not in date_str and 'T' in date_str:
                    date_str += '+00:00'

                parsed_date = datetime.fromisoformat(date_str)

                # Make sure both datetimes are timezone-aware for comparison
                if parsed_date.tzinfo is None:
                    parsed_date = parsed_date.replace(tzinfo=timezone.utc)

                if parsed_date >= cutoff_time:
                    collection['_date_used'] = date_field  # Track which field we used
                    recent_collections.append(collection)

            except (ValueError, TypeError) as e:
                print(f"⚠️  Could not parse {date_field} for collection '{collection.get('title', 'Unknown')}': {e}")
                continue

    print(f"📅 Collections with creation dates: {collections_with_dates}")
    print(f"🆕 Recent collections found: {len(recent_collections)}")

    return recent_collections

# Function to debug and show sample collection data
def debug_collection_data(max_retries=5):
    """
    Fetch a few collections to see what data is available
    """
    print("🔍 Fetching sample collections to debug data structure...")

    url = f"{store_address}/api/2023-01/custom_collections.json"
    params = {
        "limit": 5,
        "fields": "id,title,created_at,updated_at,published_at"  # Request all date fields
    }

    headers = {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": password
    }

    for attempt in range(max_retries):
        try:
            response = requests.get(url, params=params, headers=headers)
            if response.status_code == 200:
                data = response.json()
                collections = data.get('custom_collections', [])

                print(f"📊 Sample of {len(collections)} collections:")
                for i, collection in enumerate(collections[:3]):  # Show first 3
                    print(f"\n🔸 Collection {i+1}:")
                    print(f"   ID: {collection.get('id', 'N/A')}")
                    print(f"   Title: {collection.get('title', 'N/A')}")
                    print(f"   Created At: {collection.get('created_at', 'N/A')}")
                    print(f"   Published At: {collection.get('published_at', 'N/A')}")
                    print(f"   Updated At: {collection.get('updated_at', 'N/A')}")
                    print(f"   Available fields: {list(collection.keys())}")

                return collections
            else:
                print(f"Failed to fetch sample collections (attempt {attempt+1}/{max_retries}): {response.text}")
                time.sleep(1)
        except Exception as e:
            print(f"Error fetching sample collections (attempt {attempt+1}/{max_retries}): {str(e)}")
            time.sleep(1)

    print("❌ Failed to fetch sample collections")
    return []

# Function to fetch all collections with product counts from Shopify
def fetch_all_collections_with_counts(max_retries=5):
    collections = []
    page_info = None
    has_next_page = True

    while has_next_page:
        url = f"{store_address}/api/2023-01/custom_collections.json"
        params = {"limit": 250, "fields": "id,title,products_count"}  # Only get what we need

        if page_info:
            params["page_info"] = page_info

        headers = {
            "Content-Type": "application/json",
            "X-Shopify-Access-Token": password
        }

        for attempt in range(max_retries):
            try:
                response = requests.get(url, params=params, headers=headers)
                if response.status_code == 200:
                    data = response.json()
                    collections.extend(data.get('custom_collections', []))

                    # Check for pagination
                    link_header = response.headers.get('Link', '')
                    if 'rel="next"' in link_header:
                        # Extract page_info from the Link header
                        next_link = [link for link in link_header.split(',') if 'rel="next"' in link][0]
                        page_info = next_link.split('page_info=')[1].split('&')[0].split('>')[0]
                        has_next_page = True
                    else:
                        has_next_page = False

                    break
                else:
                    print(f"Failed to fetch collections (attempt {attempt+1}/{max_retries}): {response.text}")
                    time.sleep(2)  # Wait longer before retrying
            except Exception as e:
                print(f"Error fetching collections (attempt {attempt+1}/{max_retries}): {str(e)}")
                time.sleep(2)  # Wait longer before retrying
        else:
            print(f"Failed to fetch collections after {max_retries} attempts")
            has_next_page = False

    return collections

# Function to fetch all collections from Shopify (for backward compatibility)
def fetch_all_collections(max_retries=5):
    collections = []
    page_info = None
    has_next_page = True

    while has_next_page:
        url = f"{store_address}/api/2023-01/custom_collections.json"
        params = {
            "limit": 250,  # Maximum allowed by Shopify
            "fields": "id,title,created_at,updated_at,published_at"  # Include all date fields
        }

        if page_info:
            params["page_info"] = page_info

        headers = {
            "Content-Type": "application/json",
            "X-Shopify-Access-Token": password
        }

        for attempt in range(max_retries):
            try:
                response = requests.get(url, params=params, headers=headers)
                if response.status_code == 200:
                    data = response.json()
                    collections.extend(data.get('custom_collections', []))

                    # Check for pagination
                    link_header = response.headers.get('Link', '')
                    if 'rel="next"' in link_header:
                        # Extract page_info from the Link header
                        next_link = [link for link in link_header.split(',') if 'rel="next"' in link][0]
                        page_info = next_link.split('page_info=')[1].split('&')[0].split('>')[0]
                        has_next_page = True
                    else:
                        has_next_page = False

                    break
                else:
                    print(f"Failed to fetch collections (attempt {attempt+1}/{max_retries}): {response.text}")
                    time.sleep(2)  # Wait longer before retrying
            except Exception as e:
                print(f"Error fetching collections (attempt {attempt+1}/{max_retries}): {str(e)}")
                time.sleep(2)  # Wait longer before retrying
        else:
            print(f"Failed to fetch collections after {max_retries} attempts")
            has_next_page = False

    return collections

# Function to check if product is already in collection
def is_product_in_collection(collection_id, product_id, max_retries=5):
    url = f"{store_address}/api/2023-01/collections/{collection_id}/products.json"

    headers = {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": password
    }

    for attempt in range(max_retries):
        try:
            response = requests.get(url, headers=headers)
            if response.status_code == 200:
                products = response.json().get('products', [])
                return any(str(product['id']) == str(product_id) for product in products)
            else:
                print(f"Failed to check if product is in collection (attempt {attempt+1}/{max_retries}): {response.text}")
                time.sleep(2)  # Wait longer before retrying
        except Exception as e:
            print(f"Error checking if product is in collection (attempt {attempt+1}/{max_retries}): {str(e)}")
            time.sleep(2)  # Wait longer before retrying

    print(f"Failed to check if product is in collection after {max_retries} attempts")
    return False  # Assume product is not in collection if we can't check

# Function to check if collection has any products
def collection_has_products(collection_id, max_retries=5):
    url = f"{store_address}/api/2023-01/collections/{collection_id}/products.json"

    headers = {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": password
    }

    for attempt in range(max_retries):
        try:
            response = requests.get(url, headers=headers)
            if response.status_code == 200:
                products = response.json().get('products', [])
                return len(products) > 0
            else:
                print(f"Failed to check collection products (attempt {attempt+1}/{max_retries}): {response.text}")
                time.sleep(2)  # Wait longer before retrying
        except Exception as e:
            print(f"Error checking collection products (attempt {attempt+1}/{max_retries}): {str(e)}")
            time.sleep(2)  # Wait longer before retrying

    print(f"Failed to check collection products after {max_retries} attempts")
    return True  # Assume collection has products if we can't check

# Function to assign the product to the collection
def assign_product_to_collection(collection_id, product_id, max_retries=5):
    # First check if product is already in collection
    if is_product_in_collection(collection_id, product_id):
        print(f"Product ID {product_id} is already in collection ID {collection_id}")
        return True

    url = f"{store_address}/api/2023-01/collects.json"

    headers = {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": password
    }

    data = {
        "collect": {
            "product_id": product_id,
            "collection_id": collection_id
        }
    }

    for attempt in range(max_retries):
        try:
            response = requests.post(url, json=data, headers=headers)
            if response.status_code == 201:
                print(f"Product ID {product_id} added to collection ID {collection_id}")
                return True
            else:
                print(f"Failed to assign Product ID {product_id} to collection ID {collection_id} (attempt {attempt+1}/{max_retries}): {response.text}")
                time.sleep(2)  # Wait longer before retrying
        except Exception as e:
            print(f"Error assigning product to collection (attempt {attempt+1}/{max_retries}): {str(e)}")
            time.sleep(2)  # Wait longer before retrying

    print(f"Failed to assign Product ID {product_id} to collection ID {collection_id} after {max_retries} attempts")
    return False

# Function to assign product to all collections
def assign_to_all_collections(product_id):
    print("🔄 Fetching all collections...")
    collections = fetch_all_collections()
    print(f"📊 Found {len(collections)} collections")

    success_count = 0
    failure_count = 0
    already_assigned = 0

    for collection in collections:
        collection_id = collection['id']
        collection_title = collection['title']

        print(f"📝 Processing: {collection_title}")

        # Check if already assigned
        if is_product_in_collection(collection_id, product_id):
            print(f"   ⚠️  Product already in collection")
            already_assigned += 1
            continue

        if assign_product_to_collection(collection_id, product_id):
            success_count += 1
        else:
            failure_count += 1

        # Sleep to avoid rate limiting
        time.sleep(1)

    print(f"\n🎉 Assignment complete!")
    print(f"✅ Successfully assigned: {success_count}")
    print(f"⚠️  Already assigned: {already_assigned}")
    print(f"❌ Failed: {failure_count}")

# Function to check multiple collections in batch
def check_collections_batch(collections_batch):
    import concurrent.futures
    import threading

    def check_single_collection(collection):
        collection_id = collection['id']
        collection_title = collection['title']

        url = f"{store_address}/api/2023-01/collections/{collection_id}/products.json"
        params = {"limit": 1}  # Only need to check if ANY products exist

        headers = {
            "Content-Type": "application/json",
            "X-Shopify-Access-Token": password,
            "Connection": "close"  # Force connection close to avoid connection pooling issues
        }

        # Retry logic for network issues
        max_retries = 3
        for attempt in range(max_retries):
            try:
                # Create a new session for each request to avoid connection issues
                session = requests.Session()
                session.headers.update(headers)

                response = session.get(url, params=params, timeout=15)  # Increased timeout
                session.close()  # Explicitly close session

                if response.status_code == 200:
                    products = response.json().get('products', [])
                    is_empty = len(products) == 0
                    return {
                        'collection': collection,
                        'is_empty': is_empty,
                        'error': None
                    }
                elif response.status_code == 429:  # Rate limit
                    if attempt < max_retries - 1:
                        time.sleep(2)  # Wait 2 seconds and retry
                        continue
                    return {
                        'collection': collection,
                        'is_empty': False,
                        'error': f"Rate limited after {max_retries} attempts"
                    }
                else:
                    return {
                        'collection': collection,
                        'is_empty': False,
                        'error': f"HTTP {response.status_code}"
                    }

            except (requests.exceptions.ConnectionError, requests.exceptions.Timeout, requests.exceptions.SSLError) as e:
                if attempt < max_retries - 1:
                    time.sleep(1)  # Wait 1 second and retry
                    continue
                return {
                    'collection': collection,
                    'is_empty': False,
                    'error': f"Connection failed after {max_retries} attempts"
                }
            except Exception as e:
                return {
                    'collection': collection,
                    'is_empty': False,
                    'error': str(e)[:50]
                }

    # Process batch with reduced concurrency (max 2 concurrent requests for better reliability)
    results = []
    with concurrent.futures.ThreadPoolExecutor(max_workers=2) as executor:
        future_to_collection = {executor.submit(check_single_collection, collection): collection for collection in collections_batch}

        for future in concurrent.futures.as_completed(future_to_collection):
            result = future.result()
            results.append(result)
            # Small delay between requests to be more API-friendly
            time.sleep(0.2)

    return results

# Function to assign product to empty collections only
def assign_to_empty_collections(product_id):
    print("🔄 Fetching all collections...")
    collections = fetch_all_collections()
    print(f"📊 Found {len(collections)} collections")

    print("🔍 Checking collections in batches of 50 (optimized for speed)...")
    print("💡 Tip: Press Ctrl+C to cancel if taking too long")

    empty_collections = []
    checked_count = 0
    batch_size = 50

    try:
        # Process collections in batches of 50
        for i in range(0, len(collections), batch_size):
            batch = collections[i:i + batch_size]
            batch_num = (i // batch_size) + 1
            total_batches = (len(collections) + batch_size - 1) // batch_size

            print(f"   🔄 Processing batch {batch_num}/{total_batches} ({len(batch)} collections)...")

            # Check this batch
            results = check_collections_batch(batch)

            # Process results
            for result in results:
                checked_count += 1
                if result['is_empty']:
                    empty_collections.append(result['collection'])
                    print(f"   ✅ Empty: {result['collection']['title']}")
                elif result['error']:
                    print(f"   ❌ Error checking {result['collection']['title']}: {result['error']}")

            print(f"   📊 Checked {checked_count}/{len(collections)} collections, found {len(empty_collections)} empty")

            # Sleep 5 seconds between batches to avoid rate limits
            if i + batch_size < len(collections):  # Don't sleep after last batch
                print(f"   😴 Sleeping 5 seconds before next batch...")
                time.sleep(5)

    except KeyboardInterrupt:
        print(f"\n⚠️  Interrupted! Checked {checked_count} collections, found {len(empty_collections)} empty")
        if len(empty_collections) == 0:
            print("❌ No empty collections found yet")
            return

    print(f"\n📋 Found {len(empty_collections)} truly empty collections:")

    if not empty_collections:
        print("❌ No empty collections found!")
        return

    # Show all empty collections found
    for i, collection in enumerate(empty_collections):
        print(f"   {i+1}. {collection['title']}")

    # Confirm with user
    confirm = input(f"\n❓ Assign product to these {len(empty_collections)} empty collections? (y/n): ").strip().lower()
    if confirm != 'y':
        print("❌ Operation cancelled")
        return

    success_count = 0
    failure_count = 0

    for i, collection in enumerate(empty_collections):
        collection_id = collection['id']
        collection_title = collection['title']

        print(f"📝 [{i+1}/{len(empty_collections)}] Adding product to: {collection_title}")

        if assign_product_to_collection(collection_id, product_id):
            success_count += 1
        else:
            failure_count += 1

        # Sleep to avoid rate limiting
        time.sleep(1)

    print(f"\n🎉 Assignment complete!")
    print(f"✅ Successfully assigned: {success_count}")
    print(f"❌ Failed: {failure_count}")

# Function to assign product to recent collections (created in last 24h)
def assign_to_recent_collections(product_id, hours=24):
    print(f"🔄 Fetching collections created in the last {hours} hours...")
    collections = fetch_recent_collections(hours)

    if not collections:
        print(f"📭 No collections found created in the last {hours} hours!")
        return

    print(f"📊 Found {len(collections)} recent collections")

    # Show all recent collections found
    print(f"\n📋 Collections created in the last {hours} hours:")
    for i, collection in enumerate(collections):
        date_field = collection.get('_date_used', 'unknown')
        if date_field == 'created_at' and 'created_at' in collection:
            date_obj = datetime.fromisoformat(collection['created_at'].replace('Z', '+00:00'))
            print(f"   {i+1}. {collection['title']} (created: {date_obj.strftime('%Y-%m-%d %H:%M:%S UTC')})")
        elif date_field == 'published_at' and 'published_at' in collection:
            date_obj = datetime.fromisoformat(collection['published_at'].replace('Z', '+00:00'))
            print(f"   {i+1}. {collection['title']} (published: {date_obj.strftime('%Y-%m-%d %H:%M:%S UTC')})")
        else:
            print(f"   {i+1}. {collection['title']} (date not available)")

    # Confirm with user
    confirm = input(f"\n❓ Assign product to these {len(collections)} recent collections? (y/n): ").strip().lower()
    if confirm != 'y':
        print("❌ Operation cancelled")
        return

    success_count = 0
    failure_count = 0
    already_assigned = 0

    for i, collection in enumerate(collections):
        collection_id = collection['id']
        collection_title = collection['title']

        print(f"📝 [{i+1}/{len(collections)}] Processing: {collection_title}")

        # Check if already assigned
        if is_product_in_collection(collection_id, product_id):
            print(f"   ⚠️  Product already in collection")
            already_assigned += 1
            continue

        if assign_product_to_collection(collection_id, product_id):
            success_count += 1
        else:
            failure_count += 1

        # Sleep to avoid rate limiting
        time.sleep(1)

    print(f"\n🎉 Assignment complete!")
    print(f"✅ Successfully assigned: {success_count}")
    print(f"⚠️  Already assigned: {already_assigned}")
    print(f"❌ Failed: {failure_count}")

# Function to assign product to collections matching a pattern
def assign_to_matching_collections(product_id):
    print("🔄 Fetching all collections...")
    collections = fetch_all_collections()
    print(f"📊 Found {len(collections)} collections")

    # Get search pattern from user
    pattern = input("\n🔍 Enter search pattern (collections containing this text): ").strip().lower()

    if not pattern:
        print("❌ No pattern provided!")
        return

    matching_collections = []
    for collection in collections:
        if pattern in collection['title'].lower():
            matching_collections.append(collection)

    print(f"\n📋 Found {len(matching_collections)} collections matching '{pattern}':")
    for collection in matching_collections:
        print(f"   - {collection['title']}")

    if not matching_collections:
        print("❌ No matching collections found!")
        return

    # Confirm with user
    confirm = input(f"\n❓ Assign product to these {len(matching_collections)} collections? (y/n): ").strip().lower()
    if confirm != 'y':
        print("❌ Operation cancelled")
        return

    success_count = 0
    failure_count = 0
    already_assigned = 0

    for collection in matching_collections:
        collection_id = collection['id']
        collection_title = collection['title']

        print(f"📝 Processing: {collection_title}")

        # Check if already assigned
        if is_product_in_collection(collection_id, product_id):
            print(f"   ⚠️  Product already in collection")
            already_assigned += 1
            continue

        if assign_product_to_collection(collection_id, product_id):
            success_count += 1
        else:
            failure_count += 1

        # Sleep to avoid rate limiting
        time.sleep(1)

    print(f"\n🎉 Assignment complete!")
    print(f"✅ Successfully assigned: {success_count}")
    print(f"⚠️  Already assigned: {already_assigned}")
    print(f"❌ Failed: {failure_count}")

# Main menu function
def main():
    print("🚀 Product to Collections Assignment Tool")
    print("=" * 50)

    # Read product ID
    try:
        product_id = read_product_id(PRODUCT_ID_FILE_PATH)
        print(f"📦 Using product ID: {product_id}")
    except Exception as e:
        print(f"❌ Error reading product ID: {str(e)}")
        return

    # Show menu options
    print("\n📋 Choose assignment option:")
    print("1. 🌐 Assign to ALL collections")
    print("2. 📭 Assign to EMPTY collections only (no products)")
    print("3. 🔍 Assign to collections matching a pattern")
    print("4. 🕒 Assign to RECENT collections (last 24 hours)")
    print("5. 🔧 Debug: Show sample collection data")
    print("6. ❌ Exit")

    try:
        choice = input("\n🔢 Enter your choice (1-6): ").strip()

        if choice == "1":
            print("\n🌐 Assigning to ALL collections...")
            assign_to_all_collections(product_id)

        elif choice == "2":
            print("\n📭 Assigning to EMPTY collections only...")
            assign_to_empty_collections(product_id)

        elif choice == "3":
            print("\n🔍 Assigning to collections matching pattern...")
            assign_to_matching_collections(product_id)

        elif choice == "4":
            print("\n🕒 Assigning to RECENT collections (last 24 hours)...")
            # Ask user for custom hours or use default 24
            hours_input = input("⏰ Enter hours to look back (default: 24): ").strip()
            try:
                hours = int(hours_input) if hours_input else 24
                if hours <= 0:
                    print("❌ Hours must be positive! Using default 24 hours.")
                    hours = 24
            except ValueError:
                print("❌ Invalid input! Using default 24 hours.")
                hours = 24
            assign_to_recent_collections(product_id, hours)

        elif choice == "5":
            print("\n🔧 Debugging collection data...")
            debug_collection_data()

        elif choice == "6":
            print("👋 Goodbye!")
            return

        else:
            print("❌ Invalid choice! Please enter 1, 2, 3, 4, 5, or 6")

    except KeyboardInterrupt:
        print("\n❌ Operation cancelled by user")
    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    main()
