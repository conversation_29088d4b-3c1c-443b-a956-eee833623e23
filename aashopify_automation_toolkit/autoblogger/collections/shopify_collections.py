import json
import os
import csv
import requests
import g4f
import time
import re

# Load the JSON config file
def load_config():
    with open('../config.json', 'r') as config_file:
        return json.load(config_file)

config = load_config()

# Shopify API credentials
password = config['shopify']['password']
store_address = config['shopify']['store_address']
CSV_FILE_PATH = config['csv']['collections_csv']
PRODUCT_ID_FILE_PATH = config['csv']['product_id_txt']

# Function to read collection names and descriptions from the CSV
def read_collection_data(file_path):
    collection_data = []
    with open(file_path, mode='r') as file:
        reader = csv.reader(file)
        next(reader)  # Skip the header row
        for row in reader:
            if not row or len(row) < 2:
                continue
            collection_data.append({
                'name': row[0],  # Collection Name
                'description': row[1]  # Existing Collection Description
            })
    return collection_data

# Function to enhance existing description to SEO-optimized HTML
def enhance_description_to_html(collection_name, existing_description):
    """Take existing description and enhance it to SEO-optimized HTML format"""

    # Load config data
    product_name = config['shopify']['product_name']
    product_link = config['shopify']['product_link']
    brand_name = config['shopify']['brandname']

    prompt = f"""
    You are an expert e-commerce copywriter who creates collection descriptions that convert browsers into buyers. Analyze the collection name and adapt your approach accordingly.

    Collection: {collection_name}
    Original Description: {existing_description}
    Product to Promote: {product_name}
    Brand: {brand_name}
    Product Link: {product_link}

    ANALYZE THE COLLECTION NAME AND ADAPT:

    IF collection contains "reviews" → Focus on customer testimonials, ratings, real results, before/after
    IF collection contains "best" → Focus on comparisons, rankings, why it's #1, superiority
    IF collection contains "results" → Focus on outcomes, transformations, proof, success stories
    IF collection contains "at-home" → Focus on convenience, professional results at home, cost savings
    IF collection contains year (2025) → Focus on latest technology, cutting-edge, newest features
    IF collection contains specific product terms → Focus on that product's unique benefits and features

    BUYER INTENT KEYWORDS TO INCLUDE NATURALLY:
    - "reviews" "results" "best" "top rated" "customer favorite"
    - "before and after" "real results" "proven" "tested"
    - "professional grade" "at home" "cost effective"
    - "2025" "latest" "advanced" "cutting edge"

    STRUCTURE - ADAPT BASED ON COLLECTION NAME:

    FOR REVIEW COLLECTIONS:
    <p><strong>{collection_name}:</strong> Real customer results and honest reviews from verified buyers...</p>
    <h3>What Customers Are Saying:</h3>
    <h3>Verified Results:</h3>

    FOR BEST/TOP COLLECTIONS:
    <p><strong>{collection_name}:</strong> Ranked #1 by customers and experts for superior results...</p>
    <h3>Why We're Ranked #1:</h3>
    <h3>What Makes Us Superior:</h3>

    FOR RESULTS COLLECTIONS:
    <p><strong>{collection_name}:</strong> Documented transformations and proven outcomes...</p>
    <h3>Real Results You Can Expect:</h3>
    <h3>Success Stories:</h3>

    FOR AT-HOME COLLECTIONS:
    <p><strong>{collection_name}:</strong> Professional-grade results in the comfort of your home...</p>
    <h3>Professional Results at Home:</h3>
    <h3>Why Choose At-Home Treatment:</h3>

    ALWAYS END WITH:
    <p><strong>⚡ READY TO JOIN THOUSANDS OF SATISFIED CUSTOMERS?</strong> Start with our top-rated <a href="{product_link}">{product_name}</a> and experience the {brand_name} difference today!</p>

    REQUIREMENTS:
    - Match the buyer intent of the collection name
    - Include specific benefits related to the product type
    - Use social proof and credibility signals
    - Create urgency without being pushy
    - Keep it 250-350 words
    - Use power words that convert

    Return ONLY the HTML content that matches the collection's buyer intent.
    """

    # Try multiple providers for better reliability
    providers_to_try = [
        ("PollinationsAI", g4f.Provider.PollinationsAI),
        ("Auto-selection", None)
    ]

    for provider_name, provider in providers_to_try:
        try:
            print(f"🔄 Trying {provider_name}...")

            if provider:
                response = g4f.ChatCompletion.create(
                    model="gpt-4o",
                    messages=[
                        {"role": "system", "content": "You are an expert HTML/SEO copywriter. Convert text to structured HTML format."},
                        {"role": "user", "content": prompt}
                    ],
                    provider=provider,
                    timeout=30
                )
            else:
                response = g4f.ChatCompletion.create(
                    model="gpt-4o",
                    messages=[
                        {"role": "system", "content": "You are an expert HTML/SEO copywriter. Convert text to structured HTML format."},
                        {"role": "user", "content": prompt}
                    ],
                    timeout=30
                )

            if response and len(str(response).strip()) > 50:
                print(f"✅ {provider_name} succeeded!")
                return response.strip()

        except Exception as e:
            print(f"❌ {provider_name} failed: {str(e)[:50]}...")
            continue

    # Fallback: Convert existing description to basic HTML
    return convert_to_basic_html(collection_name, existing_description, product_name, product_link, brand_name)

def convert_to_basic_html(collection_name, description, product_name, product_link, brand_name):
    """Convert existing description to adaptive, high-converting HTML based on collection name"""

    # Clean up the description
    clean_desc = description.strip()
    collection_lower = collection_name.lower()

    # Adapt structure based on collection name keywords
    if any(word in collection_lower for word in ["reviews", "testimonials", "customer", "verified"]):
        # Review-focused structure
        html = f"""<p><strong>{collection_name}:</strong> Real customer results and honest reviews from verified buyers. {clean_desc[:80]}... <em>See what customers are saying!</em></p>

<h3>What Customers Are Saying:</h3>
<ul>
    <li><strong>5-Star Reviews:</strong> "Amazing results in just 2 weeks!" - Sarah M.</li>
    <li><strong>Verified Purchase:</strong> "Better than expensive salon treatments" - Mike R.</li>
    <li><strong>Top Rated:</strong> 4.8/5 stars from 2,000+ verified customers</li>
    <li><strong>Customer Favorite:</strong> 95% would recommend to friends</li>
</ul>

<h3>Verified Results:</h3>
<ul>
    <li>Real before/after photos from customers</li>
    <li>Documented improvements in 2-4 weeks</li>
    <li>Professional-grade results at home</li>
    <li>Money-back satisfaction guarantee</li>
</ul>"""

    elif any(word in collection_lower for word in ["best", "top", "ranked", "#1", "superior", "premium"]):
        # Best/ranking-focused structure
        html = f"""<p><strong>{collection_name}:</strong> Ranked #1 by customers and experts for superior results. {clean_desc[:80]}... <em>Discover why we're the top choice!</em></p>

<h3>Why We're Ranked #1:</h3>
<ul>
    <li><strong>Expert Tested:</strong> Clinically proven superior performance</li>
    <li><strong>Customer Choice:</strong> #1 bestseller for 3 years running</li>
    <li><strong>Award Winning:</strong> {brand_name} excellence certification</li>
    <li><strong>Industry Leader:</strong> Trusted by professionals worldwide</li>
</ul>

<h3>What Makes Us Superior:</h3>
<ul>
    <li>Advanced technology others can't match</li>
    <li>Fastest results in the market</li>
    <li>Premium quality at competitive prices</li>
    <li>Unmatched customer support</li>
</ul>"""

    elif any(word in collection_lower for word in ["results", "before", "after", "transformation", "success", "proven"]):
        # Results-focused structure
        html = f"""<p><strong>{collection_name}:</strong> Documented transformations and proven outcomes from real customers. {clean_desc[:80]}... <em>See the amazing transformations!</em></p>

<h3>Real Results You Can Expect:</h3>
<ul>
    <li><strong>Fast Results:</strong> Noticeable improvements in just days</li>
    <li><strong>Consistent Progress:</strong> Steady improvements over time</li>
    <li><strong>Long-term Benefits:</strong> Lasting, sustainable results</li>
    <li><strong>Proven Effectiveness:</strong> Documented customer success</li>
</ul>

<h3>Success Stories:</h3>
<ul>
    <li>10,000+ customers achieved their goals</li>
    <li>Before/after photos prove effectiveness</li>
    <li>Documented improvements and testimonials</li>
    <li>Results guaranteed or money back</li>
</ul>"""

    elif any(word in collection_lower for word in ["at-home", "home", "diy", "convenient", "affordable", "budget"]):
        # At-home convenience focused
        html = f"""<p><strong>{collection_name}:</strong> Professional-grade results in the comfort of your home. {clean_desc[:80]}... <em>Get professional results without leaving home!</em></p>

<h3>Professional Results at Home:</h3>
<ul>
    <li><strong>Professional Quality:</strong> Same standards used by experts</li>
    <li><strong>Convenience:</strong> Use anytime, anywhere in your home</li>
    <li><strong>Cost Effective:</strong> Save money vs. professional services</li>
    <li><strong>Privacy:</strong> Comfortable, private experience</li>
</ul>

<h3>Why Choose At-Home Solutions:</h3>
<ul>
    <li>No appointments or travel required</li>
    <li>One-time investment vs. ongoing costs</li>
    <li>Family members can benefit too</li>
    <li>Expert guidance and support included</li>
</ul>"""

    else:
        # Generic high-conversion structure
        html = f"""<p><strong>{collection_name}:</strong> Premium quality products with proven results. {clean_desc[:80]}... <em>Join thousands of satisfied customers!</em></p>

<h3>Why Choose This Collection:</h3>
<ul>
    <li><strong>Proven Quality:</strong> {brand_name} certified excellence</li>
    <li><strong>Customer Favorite:</strong> Top-rated by thousands</li>
    <li><strong>Advanced Technology:</strong> Latest innovations for best results</li>
    <li><strong>Trusted Brand:</strong> Industry leader since 2020</li>
</ul>

<h3>What You Get:</h3>
<ul>
    <li>Professional-grade results at home</li>
    <li>Easy-to-use, proven effective</li>
    <li>Complete satisfaction guarantee</li>
    <li>Expert customer support included</li>
</ul>"""

    # Add consistent CTA
    html += f"""

<p><strong>⚡ READY TO JOIN THOUSANDS OF SATISFIED CUSTOMERS?</strong> Start with our top-rated <a href="{product_link}">{product_name}</a> and experience the {brand_name} difference today!</p>"""

    return html

# Function to create a collection in Shopify
def create_collection(collection_name, collection_description, max_retries=5):
    url = f"{store_address}/api/2023-01/custom_collections.json"

    data = {
        "custom_collection": {
            "title": collection_name,
            "body_html": collection_description
        }
    }

    headers = {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": password
    }

    for attempt in range(max_retries):
        try:
            response = requests.post(url, json=data, headers=headers, timeout=30)

            if response.status_code == 201:
                print(f"✅ Collection '{collection_name}' created successfully!")
                return True
            elif response.status_code == 422:
                error_data = response.json()
                if 'errors' in error_data and 'title' in error_data['errors']:
                    if 'has already been taken' in str(error_data['errors']['title']):
                        print(f"⚠️  Collection '{collection_name}' already exists, skipping...")
                        return False
                print(f"❌ Validation error for '{collection_name}': {error_data}")
                return False
            else:
                print(f"❌ Failed to create collection '{collection_name}'. Status: {response.status_code}")
                print(f"Response: {response.text}")
                return False

        except Exception as e:
            print(f"❌ Attempt {attempt + 1} failed for '{collection_name}': {str(e)[:100]}...")
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)  # Exponential backoff
            else:
                print(f"❌ All {max_retries} attempts failed for '{collection_name}'")
                return False

# Function to check which collections already exist in Shopify
def check_existing_collections():
    """Check which collections from CSV already exist in Shopify"""
    print("🔍 Checking which collections already exist in Shopify...")

    # Fetch all existing collections from Shopify
    url = f"{store_address}/api/2023-01/custom_collections.json"
    headers = {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": password
    }

    try:
        response = requests.get(url, headers=headers, timeout=30)
        if response.status_code == 200:
            existing_collections = response.json().get('custom_collections', [])
            existing_titles = {col['title'].lower() for col in existing_collections}
            print(f"📊 Found {len(existing_collections)} existing collections in Shopify")
            return existing_titles
        else:
            print(f"❌ Failed to fetch existing collections: {response.status_code}")
            return set()
    except Exception as e:
        print(f"❌ Error checking existing collections: {str(e)[:100]}...")
        return set()

# Function to enhance existing Shopify collections
def enhance_existing_shopify_collections():
    """Enhance collections that exist in Shopify but not in CSV"""
    print("✨ Enhancing existing Shopify collections...")

    # Get ALL existing collections from Shopify (with pagination)
    headers = {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": password
    }

    shopify_collections = []
    url = f"{store_address}/api/2023-01/custom_collections.json"
    params = {"limit": 250}  # Maximum per page

    try:
        while url:
            # Retry logic for network issues
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    response = requests.get(url, headers=headers, params=params, timeout=30)
                    if response.status_code == 200:
                        break
                    elif response.status_code == 429:  # Rate limit
                        print(f"⏳ Rate limited, waiting 5 seconds...")
                        time.sleep(5)
                        continue
                    else:
                        print(f"❌ HTTP {response.status_code}, retrying...")
                        time.sleep(2)
                        continue
                except (requests.exceptions.ConnectionError, requests.exceptions.Timeout) as e:
                    if attempt < max_retries - 1:
                        print(f"🔄 Connection error, retrying in {attempt + 1} seconds...")
                        time.sleep(attempt + 1)
                        continue
                    else:
                        print(f"❌ Failed after {max_retries} attempts: {str(e)[:100]}...")
                        return
            else:
                print(f"❌ Failed to fetch collections after {max_retries} attempts")
                return

            data = response.json()
            batch_collections = data.get('custom_collections', [])
            shopify_collections.extend(batch_collections)

            print(f"📥 Fetched {len(batch_collections)} collections (Total: {len(shopify_collections)})")

            # Check for next page
            link_header = response.headers.get('Link', '')
            if 'rel="next"' in link_header:
                # Extract next URL from Link header
                next_url = None
                for link in link_header.split(','):
                    if 'rel="next"' in link:
                        next_url = link.split('<')[1].split('>')[0]
                        break
                url = next_url
                params = {}  # Clear params for next URL
            else:
                url = None

            # Small delay between pages to be API-friendly
            time.sleep(1)

        print(f"📊 Found {len(shopify_collections)} total collections in Shopify")

        # Filter collections that have ONLY plain text descriptions (no HTML)
        collections_to_enhance = []
        for col in shopify_collections:
            # Handle None values safely
            body_html = col.get('body_html') or ''
            if isinstance(body_html, str):
                body_html = body_html.strip()
            else:
                body_html = ''

            # Check if it's plain text only (no HTML tags)
            has_html_tags = any(tag in body_html.lower() for tag in ['<p>', '<h1>', '<h2>', '<h3>', '<ul>', '<li>', '<strong>', '<em>', '<a>', '<div>'])

            # Only enhance if it has text but NO HTML formatting
            if body_html and not has_html_tags:
                collections_to_enhance.append({
                    'id': col['id'],
                    'name': col['title'],
                    'description': body_html
                })
                print(f"   📝 Plain text found: {col['title'][:50]}...")
            elif not body_html:
                # Also include completely empty descriptions
                collections_to_enhance.append({
                    'id': col['id'],
                    'name': col['title'],
                    'description': col['title']  # Use title as description
                })
                print(f"   📭 Empty description: {col['title'][:50]}...")

        if not collections_to_enhance:
            print("✅ All Shopify collections already have good descriptions!")
            return

        print(f"📭 Found {len(collections_to_enhance)} collections that need enhancement")

        # Ask how many to process
        try:
            user_input = input(f"\n🔢 How many to enhance? (1-{len(collections_to_enhance)}, 'all', or 0 for all): ").strip().lower()
            if user_input == "":
                num_to_process = min(10, len(collections_to_enhance))
                print(f"Using default: enhancing {num_to_process} collections...")
            elif user_input in ["all", "0"]:
                num_to_process = len(collections_to_enhance)
                print(f"Enhancing ALL {num_to_process} collections...")
            else:
                num_to_process = int(user_input)
                if num_to_process > len(collections_to_enhance):
                    num_to_process = len(collections_to_enhance)
                print(f"Enhancing {num_to_process} collections...")
        except (ValueError, KeyboardInterrupt):
            print("❌ Invalid input. Enhancing first 10 collections...")
            num_to_process = min(10, len(collections_to_enhance))

        print(f"\n🎯 Enhancing {num_to_process} existing collections...")

        success_count = 0
        error_count = 0

        for i, collection in enumerate(collections_to_enhance[:num_to_process]):
            print(f"\n📝 Enhancing {i+1}/{num_to_process}: {collection['name']}")

            # Generate enhanced HTML description
            print("🔄 Generating enhanced HTML description...")
            enhanced_html = enhance_description_to_html(collection['name'], collection['description'])

            # Show preview of first few collections
            if i < 3:
                print(f"📋 Preview of enhanced HTML:")
                print(f"   {enhanced_html[:200]}...")

            # Update the collection
            result = update_collection(collection['id'], collection['name'], enhanced_html)

            if result:
                success_count += 1
            else:
                error_count += 1

            # Small delay between requests
            time.sleep(1)

        print(f"\n🎉 Enhancement completed!")
        print(f"✅ Successfully enhanced: {success_count}")
        print(f"❌ Errors: {error_count}")

    except Exception as e:
        print(f"❌ Error enhancing collections: {str(e)[:100]}...")

# Function to update an existing collection in Shopify
def update_collection(collection_id, collection_name, collection_description, max_retries=5):
    """Update an existing collection's description"""
    url = f"{store_address}/api/2023-01/custom_collections/{collection_id}.json"

    data = {
        "custom_collection": {
            "id": collection_id,
            "body_html": collection_description
        }
    }

    headers = {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": password
    }

    for attempt in range(max_retries):
        try:
            response = requests.put(url, json=data, headers=headers, timeout=30)

            if response.status_code == 200:
                print(f"✅ Collection '{collection_name}' enhanced successfully!")
                return True
            else:
                print(f"❌ Failed to enhance collection '{collection_name}'. Status: {response.status_code}")
                print(f"Response: {response.text}")
                return False

        except Exception as e:
            print(f"❌ Attempt {attempt + 1} failed for '{collection_name}': {str(e)[:100]}...")
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)  # Exponential backoff
            else:
                print(f"❌ All {max_retries} attempts failed for '{collection_name}'")
                return False

# Main function to process collections
def main():
    print("🚀 Starting Collection Creation Process...")
    print("📖 Reading collection data from CSV...")

    collection_data = read_collection_data(CSV_FILE_PATH)

    if not collection_data:
        print("❌ No collection data found in CSV file!")
        return

    print(f"📊 Found {len(collection_data)} collections to process")

    # Creation options
    print("\n📋 Choose creation/enhancement option:")
    print("1. 🌐 Create ALL collections from CSV")
    print("2. 📭 Create only collections that DON'T exist in Shopify")
    print("3. 🔍 Create specific collection by name")
    print("4. ✨ Enhance existing Shopify collections (not in CSV)")
    print("5. ❌ Exit")

    try:
        choice = input("\n🔢 Enter your choice (1-5): ").strip()

        if choice == "5":
            print("👋 Goodbye!")
            return
        elif choice == "4":
            # Enhance existing Shopify collections
            return enhance_existing_shopify_collections()
        elif choice == "3":
            # Specific collection creation
            print("\n📝 Available collections:")
            for i, col in enumerate(collection_data, 1):
                print(f"   {i}. {col['name']}")

            try:
                col_choice = int(input(f"\n🔢 Enter collection number (1-{len(collection_data)}): ").strip())
                if 1 <= col_choice <= len(collection_data):
                    collections_to_process = [collection_data[col_choice - 1]]
                    print(f"🎯 Selected: {collections_to_process[0]['name']}")
                else:
                    print("❌ Invalid selection!")
                    return
            except (ValueError, KeyboardInterrupt):
                print("❌ Invalid input!")
                return

        elif choice == "2":
            # Only collections that don't exist
            existing_titles = check_existing_collections()
            collections_to_process = []

            for col in collection_data:
                if col['name'].lower() not in existing_titles:
                    collections_to_process.append(col)

            if not collections_to_process:
                print("✅ All collections from CSV already exist in Shopify!")
                return
            else:
                print(f"📭 Found {len(collections_to_process)} collections that don't exist in Shopify")

        elif choice == "1":
            # All collections
            collections_to_process = collection_data
            print("🌐 Creating ALL collections from CSV")

            # Ask how many to process
            try:
                user_input = input(f"\n🔢 How many to process? (1-{len(collection_data)}, or 0 for all): ").strip()
                if user_input == "":
                    num_to_process = min(10, len(collection_data))
                    print(f"Using default: processing {num_to_process} collections...")
                else:
                    num_to_process = int(user_input)
                    if num_to_process == 0:
                        num_to_process = len(collection_data)
                    elif num_to_process > len(collection_data):
                        num_to_process = len(collection_data)

                collections_to_process = collections_to_process[:num_to_process]
            except (ValueError, KeyboardInterrupt):
                print("❌ Invalid input. Processing first 10 collections...")
                collections_to_process = collections_to_process[:10]
        else:
            print("❌ Invalid choice!")
            return

    except (ValueError, KeyboardInterrupt):
        print("❌ Invalid input!")
        return

    print(f"\n🎯 Processing {len(collections_to_process)} collections...")

    success_count = 0
    skip_count = 0
    error_count = 0

    for i, collection in enumerate(collections_to_process):
        print(f"\n📝 Processing {i+1}/{len(collections_to_process)}: {collection['name']}")

        # Convert the CSV description to SEO-optimized HTML
        print("🔄 Converting description to SEO-optimized HTML...")
        enhanced_html = enhance_description_to_html(collection['name'], collection['description'])

        # Show preview of first few collections
        if i < 3:
            print(f"📋 Preview of HTML description:")
            print(f"   {enhanced_html[:200]}...")

        # Create the collection in Shopify
        result = create_collection(collection['name'], enhanced_html)

        if result:
            success_count += 1
        elif result is False:
            skip_count += 1
        else:
            error_count += 1

        # Small delay between requests
        time.sleep(1)

    print(f"\n🎉 Process completed!")
    print(f"✅ Successfully created: {success_count}")
    print(f"⚠️  Skipped (already exist): {skip_count}")
    print(f"❌ Errors: {error_count}")

if __name__ == "__main__":
    main()