#!/usr/bin/env python3
"""
Master Autoblogger Automation Script
Orchestrates all autoblogger operations with optimizations
"""

import json
import os
import sys
import time
import subprocess
import requests
from datetime import datetime
from requests.exceptions import SSLError, ConnectionError, Timeout
from urllib3.exceptions import MaxRetryError
from pathlib import Path

def load_config():
    """Load configuration from config.json"""
    try:
        with open('config.json', 'r') as config_file:
            return json.load(config_file)
    except FileNotFoundError:
        print("❌ Config file not found. Please make sure config.json exists.")
        sys.exit(1)

def run_script(script_path, description, args=None):
    """Run a script with error handling and timing"""
    print(f"\n🔄 {description}...")
    print(f"🔧 Executing: {script_path}")
    start_time = time.time()

    try:
        # Get the directory where the script is located
        script_dir = os.path.dirname(script_path)
        script_name = os.path.basename(script_path)

        # Save current directory
        original_dir = os.getcwd()

        # Change to the script's directory if it has one
        if script_dir:
            print(f"📁 Changing to directory: {script_dir}")
            os.chdir(script_dir)
            cmd = [sys.executable, script_name]
        else:
            cmd = [sys.executable, script_path]

        if args:
            cmd.extend(args)

        print(f"▶️  Running: {' '.join(cmd)}")
        print("=" * 50)

        # Run interactively so user can provide input
        result = subprocess.run(cmd, timeout=3600)

        # Change back to original directory
        os.chdir(original_dir)

        elapsed = time.time() - start_time
        print("=" * 50)

        if result.returncode == 0:
            print(f"✅ {description} completed successfully in {elapsed:.1f}s")
            return True
        else:
            print(f"❌ {description} failed with return code: {result.returncode}")
            return False

    except subprocess.TimeoutExpired:
        print(f"⏰ {description} timed out after 1 hour")
        try:
            os.chdir(original_dir)
        except:
            pass
        return False
    except Exception as e:
        print(f"❌ Error running {description}: {e}")
        try:
            os.chdir(original_dir)
        except:
            pass
        return False

def run_script_in_new_terminal(script_path, description):
    """Run a script in a new terminal window"""
    try:
        # Get the directory where the script is located
        script_dir = os.path.dirname(script_path)
        script_name = os.path.basename(script_path)

        # Build the command to run in new terminal
        if script_dir:
            cmd = f"cd '{script_dir}' && python3 '{script_name}'"
        else:
            cmd = f"python3 '{script_path}'"

        # Try different terminal emulators
        terminal_commands = [
            f"gnome-terminal -- bash -c \"{cmd}; echo 'Press Enter to close...'; read\"",
            f"xterm -e \"bash -c '{cmd}; echo Press Enter to close...; read'\"",
            f"konsole -e bash -c \"{cmd}; echo 'Press Enter to close...'; read\"",
            f"xfce4-terminal -e \"bash -c '{cmd}; echo Press Enter to close...; read'\"",
        ]

        print(f"🚀 Opening {description} in new terminal...")
        print(f"📁 Script: {script_path}")

        # Try each terminal command until one works
        for terminal_cmd in terminal_commands:
            try:
                subprocess.Popen(terminal_cmd, shell=True)
                break
            except Exception:
                continue
        else:
            # If no terminal worked, fall back to background execution
            print("⚠️  No terminal emulator found, running in background...")
            subprocess.Popen(f"bash -c \"{cmd}\"", shell=True)

        print(f"✅ {description} launched in new terminal")
        return True

    except Exception as e:
        print(f"❌ Error opening {description} in new terminal: {e}")
        return False

def create_blogs_optimized():
    """Create blogs with blog schema selection"""
    print("\n📝 BLOG CREATION - Schema Selection")
    print("=" * 50)

    # Available blog schemas
    blog_schemas = {
        '1': ('blogs/blogschemas/blogschema10.py', 'Blog Schema 10'),
        '2': ('blogs/blogschemas/blogschema20.py', 'Blog Schema 20'),
        '3': ('blogs/blogschemas/blogschema30.py', 'Blog Schema 30'),
        '4': ('blogs/blogschemas/blogschema40.py', 'Blog Schema 40'),
        '5': ('blogs/blogschemas/blogschema50.py', 'Blog Schema 50'),
        '6': ('blogs/blogschemas/blogschema60.py', 'Blog Schema 60'),
    }

    print("📋 Available Blog Schemas:")
    for key, (path, name) in blog_schemas.items():
        print(f"   {key}. {name}")

    print("\n💡 Instructions:")
    print("   - Enter numbers separated by commas (e.g., 1,3,5)")
    print("   - Each selected schema will open in a new terminal")
    print("   - Press Enter without input to cancel")

    # Get user selection
    selection = input("\n🔢 Select schemas to run: ").strip()

    if not selection:
        print("❌ No schemas selected. Returning to main menu.")
        return False

    # Parse selection
    try:
        selected_numbers = [num.strip() for num in selection.split(',')]
        selected_schemas = []

        for num in selected_numbers:
            if num in blog_schemas:
                selected_schemas.append(blog_schemas[num])
            else:
                print(f"⚠️  Invalid selection: {num} (ignored)")

        if not selected_schemas:
            print("❌ No valid schemas selected.")
            return False

        print(f"\n🚀 Launching {len(selected_schemas)} blog schemas in new terminals...")

        success_count = 0
        for script_path, description in selected_schemas:
            if run_script_in_new_terminal(script_path, description):
                success_count += 1
            time.sleep(1)  # Small delay between launches

        print(f"\n🎉 Successfully launched {success_count}/{len(selected_schemas)} schemas!")
        print("💡 Check the new terminal windows to monitor progress.")

        return success_count > 0

    except Exception as e:
        print(f"❌ Error processing selection: {e}")
        return False

def create_collections_optimized():
    """Create collections with optimization"""
    print("\n📁 COLLECTION CREATION (Optimized)")
    return run_script('collections/shopify_collections.py', 'Creating collections from CSV')

def assign_products_to_recent_collections():
    """Assign products to recent collections only"""
    print("\n🔗 PRODUCT ASSIGNMENT (Recent Collections Only)")

    # Use the optimized recent collections option
    return run_script('collections/assign_product_to_collections.py', 'Assigning products to recent collections')

def generate_seo_meta_recent_blogs():
    """Generate SEO meta for recent blogs only"""
    print("\n🎯 SEO META GENERATION (Recent Blogs)")

    return run_script('SEOmetaScript/shopify_blog_meta.py', 'Generating SEO meta for recent blogs')

def generate_seo_meta_recent_collections():
    """Generate SEO meta for recent collections only"""
    print("\n🎯 SEO META GENERATION (Recent Collections)")

    return run_script('SEOmetaScript/shopify_collection_meta.py', 'Generating SEO meta for recent collections')

def remove_duplicate_blogs():
    """Remove duplicate blogs automatically"""
    print("\n🧹 DUPLICATE CLEANUP")

    return run_script('duplicate_removers/auto_remove_duplicate_blogs.py', 'Removing duplicate blogs', ['--batch', '20'])

def remove_duplicate_collections():
    """Remove duplicate collections automatically"""
    print("\n🧹 DUPLICATE CLEANUP")

    return run_script('duplicate_removers/auto_remove_duplicate_collections.py', 'Removing duplicate collections')

def run_full_automation():
    """Run complete automation workflow"""
    print("\n🚀 FULL AUTOMATION WORKFLOW")
    print("=" * 50)

    start_time = time.time()
    results = {}

    # Step 1: Create blogs
    results['blogs'] = create_blogs_optimized()

    # Step 2: Create collections
    results['collections'] = create_collections_optimized()

    # Step 3: Assign products to recent collections
    results['product_assignment'] = assign_products_to_recent_collections()

    # Step 4: Generate SEO meta for recent blogs
    results['blog_seo'] = generate_seo_meta_recent_blogs()

    # Step 5: Generate SEO meta for recent collections
    results['collection_seo'] = generate_seo_meta_recent_collections()

    # Step 6: Clean up duplicates
    results['duplicate_blogs'] = remove_duplicate_blogs()
    results['duplicate_collections'] = remove_duplicate_collections()

    # Summary
    total_time = time.time() - start_time
    successful = sum(1 for success in results.values() if success)
    total_steps = len(results)

    print(f"\n🎉 AUTOMATION COMPLETE!")
    print(f"⏱️  Total time: {total_time/60:.1f} minutes")
    print(f"✅ Successful steps: {successful}/{total_steps}")
    print(f"❌ Failed steps: {total_steps - successful}/{total_steps}")

    # Show detailed results
    print(f"\n📊 DETAILED RESULTS:")
    for step, success in results.items():
        status = "✅" if success else "❌"
        print(f"   {status} {step.replace('_', ' ').title()}")

def bulk_add_images():
    """Bulk add images to blogs and collections"""
    print("\n🖼️ BULK IMAGE ADDITION")
    print("=" * 50)
    print("Add the same image to multiple blogs/collections for higher conversion rates!")

    # Get image URL first
    image_url = input("\n🖼️ Enter your image URL: ").strip()
    if not image_url.startswith(('http://', 'https://')):
        print("❌ Please enter a valid URL starting with http:// or https://")
        return False

    print(f"✅ Image URL: {image_url}")

    # Choose what to add image to
    print("\n📋 What do you want to add this image to?")
    print("1. ALL blogs that don't have images")
    print("2. ALL collections that don't have images")
    print("3. ALL blogs (even if they have images)")
    print("4. ALL collections (even if they have images)")

    choice = input("\nChoose (1-4): ").strip()

    # Choose image position
    print("\n📍 Where to place the image?")
    print("1. TOP (above content) - BEST for collections & conversion")
    print("2. BOTTOM (below content) - BEST for blogs & SEO")

    position_choice = input("\nChoose position (1-2): ").strip()
    position = "top" if position_choice == "1" else "bottom"

    position_text = "TOP (above content)" if position == "top" else "BOTTOM (below content)"
    print(f"✅ Position: {position_text}")

    # Load config for API access
    config = load_config()
    password = config['shopify']['password']
    store_address = config['shopify']['store_address']

    headers = {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': password
    }

    # Check network connectivity before starting
    if not check_network_connectivity(store_address, headers):
        print("❌ Cannot proceed with bulk operations due to network issues")
        return False

    if choice in ["1", "3"]:
        # Process blogs
        return process_blogs_bulk_images(image_url, position, choice == "1", headers, store_address)
    elif choice in ["2", "4"]:
        # Process collections
        return process_collections_bulk_images(image_url, position, choice == "2", headers, store_address)
    else:
        print("❌ Invalid choice!")
        return False

def process_blogs_bulk_images(image_url, position, only_without_images, headers, store_address):
    """Process bulk image addition to blogs"""
    print("\n🔄 Getting your blogs...")

    # Get all blogs and articles
    blogs = get_all_blogs(headers, store_address)
    if not blogs:
        print("❌ No blogs found!")
        return False

    if only_without_images:
        target_blogs = [blog for blog in blogs if not has_image(blog['body_html'])]
        print(f"📊 Found {len(target_blogs)} blogs WITHOUT images")
    else:
        target_blogs = blogs
        print(f"📊 Found {len(target_blogs)} total blogs")

    if not target_blogs:
        print("✅ All blogs already have images!")
        return True

    # Confirm
    confirm = input(f"\n✅ Add image to {len(target_blogs)} blogs? (y/n): ").strip().lower()
    if confirm != 'y':
        print("❌ Cancelled")
        return False

    # Add images
    print(f"\n🔄 Adding images to {len(target_blogs)} blogs...")
    success = 0

    for i, blog in enumerate(target_blogs, 1):
        print(f"📝 {i}/{len(target_blogs)}: {blog['title'][:50]}...")

        new_content = add_image_to_content(blog['body_html'], image_url, position)
        if update_blog_content(blog['blog_id'], blog['id'], new_content, headers, store_address):
            success += 1
            print("   ✅ Done")
        else:
            print("   ❌ Failed")

        time.sleep(1)  # Be nice to API

    print(f"\n🎉 Successfully added images to {success}/{len(target_blogs)} blogs!")
    return success > 0

def process_collections_bulk_images(image_url, position, only_without_images, headers, store_address):
    """Process bulk image addition to collections"""
    print("\n🔄 Getting your collections...")

    # Get all collections
    collections = get_all_collections(headers, store_address)
    if not collections:
        print("❌ No collections found!")
        return False

    if only_without_images:
        target_collections = [col for col in collections if not has_image(col['body_html'])]
        print(f"📊 Found {len(target_collections)} collections WITHOUT images")
    else:
        target_collections = collections
        print(f"📊 Found {len(target_collections)} total collections")

    if not target_collections:
        print("✅ All collections already have images!")
        return True

    # Confirm
    confirm = input(f"\n✅ Add image to {len(target_collections)} collections? (y/n): ").strip().lower()
    if confirm != 'y':
        print("❌ Cancelled")
        return False

    # Add images
    print(f"\n🔄 Adding images to {len(target_collections)} collections...")
    success = 0

    for i, col in enumerate(target_collections, 1):
        print(f"🏷️ {i}/{len(target_collections)}: {col['title'][:50]}...")

        new_content = add_image_to_content(col['body_html'], image_url, position)
        if update_collection_content(col['id'], col['type'], new_content, headers, store_address):
            success += 1
            print("   ✅ Done")
        else:
            print("   ❌ Failed")

        time.sleep(1)  # Be nice to API

    print(f"\n🎉 Successfully added images to {success}/{len(target_collections)} collections!")
    return success > 0

def show_optimization_tips():
    """Show optimization tips and time-saving suggestions"""
    print("\n💡 OPTIMIZATION TIPS:")
    print("=" * 50)
    print("1. 🕒 Use 'Recent only' options to process only new items")
    print("2. 📊 Run full automation during off-peak hours")
    print("3. 🔄 Check progress regularly and resume if needed")
    print("4. 📝 Prepare CSV files in advance for faster processing")
    print("5. 🧹 Run duplicate cleanup after bulk operations")
    print("6. ⚡ Use batch processing for better performance")
    print("7. 📈 Monitor API rate limits to avoid delays")
    print("8. 🖼️ Add images to collections first (higher conversion impact)")
    print("9. 💰 Use TOP position for collections, BOTTOM for blogs")

# Helper functions for network resilience
def check_network_connectivity(store_address, headers, max_retries=3):
    """Check if we can connect to Shopify API before starting bulk operations"""
    print("🔍 Checking network connectivity to Shopify...")

    # Simple test endpoint - get shop info
    url = f"{store_address}/api/2023-01/shop.json"

    for attempt in range(max_retries):
        try:
            response = requests.get(url, headers=headers, timeout=30)
            if response.status_code == 200:
                print("✅ Network connectivity confirmed")
                return True
            else:
                print(f"⚠️ HTTP {response.status_code} on connectivity test attempt {attempt+1}")
        except Exception as e:
            print(f"❌ Connectivity test failed attempt {attempt+1}: {type(e).__name__}")
            if attempt < max_retries - 1:
                time.sleep(5)

    print("❌ Network connectivity issues detected. Consider checking your internet connection.")
    return False

# Helper functions for image addition
def get_all_blogs(headers, store_address):
    """Get all blog posts from Shopify"""
    all_blog_posts = []

    # Get blogs
    url = f"{store_address}/api/2023-01/blogs.json"
    response = requests.get(url, headers=headers)
    if response.status_code != 200:
        return []

    blogs = response.json().get('blogs', [])

    # Get articles from all blogs
    for blog in blogs:
        blog_id = blog['id']
        print(f"📥 Getting articles from blog: {blog['title']}")

        # Get articles with pagination
        page_url = f"{store_address}/api/2023-01/blogs/{blog_id}/articles.json?limit=250"

        while page_url:
            response = requests.get(page_url, headers=headers)
            if response.status_code == 200:
                data = response.json()
                articles = data.get('articles', [])

                for article in articles:
                    all_blog_posts.append({
                        'id': article['id'],
                        'title': article['title'],
                        'body_html': article.get('body_html', ''),
                        'blog_id': blog_id
                    })

                # Check for next page
                link_header = response.headers.get('Link', '')
                if 'rel="next"' in link_header:
                    for link in link_header.split(','):
                        if 'rel="next"' in link:
                            page_url = link.split('<')[1].split('>')[0]
                            break
                    else:
                        page_url = None
                else:
                    page_url = None

                time.sleep(0.5)  # Be nice to API
            else:
                break

    print(f"📊 Found {len(all_blog_posts)} total blog posts")
    return all_blog_posts

def get_all_collections(headers, store_address):
    """Get all collections from Shopify"""
    all_collections = []

    # Get custom collections
    url = f"{store_address}/api/2023-01/custom_collections.json?limit=250"
    while url:
        response = requests.get(url, headers=headers)
        if response.status_code == 200:
            data = response.json()
            collections = data.get('custom_collections', [])

            for col in collections:
                all_collections.append({
                    'id': col['id'],
                    'title': col['title'],
                    'body_html': col.get('body_html', ''),
                    'type': 'custom_collections'
                })

            # Check for next page
            link_header = response.headers.get('Link', '')
            if 'rel="next"' in link_header:
                for link in link_header.split(','):
                    if 'rel="next"' in link:
                        url = link.split('<')[1].split('>')[0]
                        break
                else:
                    url = None
            else:
                url = None

            time.sleep(0.5)  # Be nice to API
        else:
            break

    # Get smart collections
    url = f"{store_address}/api/2023-01/smart_collections.json?limit=250"
    while url:
        response = requests.get(url, headers=headers)
        if response.status_code == 200:
            data = response.json()
            collections = data.get('smart_collections', [])

            for col in collections:
                all_collections.append({
                    'id': col['id'],
                    'title': col['title'],
                    'body_html': col.get('body_html', ''),
                    'type': 'smart_collections'
                })

            # Check for next page
            link_header = response.headers.get('Link', '')
            if 'rel="next"' in link_header:
                for link in link_header.split(','):
                    if 'rel="next"' in link:
                        url = link.split('<')[1].split('>')[0]
                        break
                else:
                    url = None
            else:
                url = None

            time.sleep(0.5)  # Be nice to API
        else:
            break

    print(f"📊 Found {len(all_collections)} total collections")
    return all_collections

def add_image_to_content(content, image_url, position="top"):
    """Add image at top or bottom"""
    # Handle None content
    if content is None:
        content = ""

    image_html = f'<img src="{image_url}" style="max-width: 100%; height: auto; margin: 20px auto; display: block;" />'

    if position == "top":
        return image_html + '\n\n' + content
    else:  # bottom
        return content + '\n\n' + image_html

def has_image(content):
    """Check if content already has an image"""
    return '<img' in content.lower() if content else False

def update_blog_content(blog_id, article_id, new_content, headers, store_address, max_retries=15):
    """Update blog with new content - with robust error handling and retries"""
    url = f"{store_address}/api/2023-01/blogs/{blog_id}/articles/{article_id}.json"

    data = {
        "article": {
            "id": article_id,
            "body_html": new_content
        }
    }

    # Create a session for better connection handling
    session = requests.Session()

    for attempt in range(max_retries):
        try:
            # Use longer timeout and session for better connection handling
            response = session.put(url, json=data, headers=headers, timeout=90)

            if response.status_code == 200:
                session.close()
                return True
            else:
                print(f"   ⚠️ HTTP {response.status_code} on attempt {attempt+1}/{max_retries}")
                if attempt < max_retries - 1:
                    print(f"   ⏳ Waiting 3s before retry...")
                    time.sleep(3)

        except (SSLError, MaxRetryError, ConnectionError, Timeout) as e:
            print(f"   🔄 Network error on attempt {attempt+1}/{max_retries}: {type(e).__name__}")
            if attempt < max_retries - 1:
                print(f"   ⏳ Network issue - waiting 3s before retry...")
                time.sleep(3)
        except Exception as e:
            print(f"   ❌ Unexpected error on attempt {attempt+1}/{max_retries}: {e}")
            if attempt < max_retries - 1:
                print(f"   ⏳ Waiting 3s before retry...")
                time.sleep(3)

    session.close()
    print(f"   ❌ Failed to update blog after {max_retries} attempts")
    return False

def update_collection_content(collection_id, collection_type, new_content, headers, store_address, max_retries=15):
    """Update collection with new content - with robust error handling and retries"""
    url = f"{store_address}/api/2023-01/{collection_type}/{collection_id}.json"

    # Remove 's' from collection type for the data key
    data_key = collection_type.rstrip('s')

    data = {
        data_key: {
            "id": collection_id,
            "body_html": new_content
        }
    }

    # Create a session for better connection handling
    session = requests.Session()

    for attempt in range(max_retries):
        try:
            # Use longer timeout and session for better connection handling
            response = session.put(url, json=data, headers=headers, timeout=90)

            if response.status_code == 200:
                session.close()
                return True
            else:
                print(f"   ⚠️ HTTP {response.status_code} on attempt {attempt+1}/{max_retries}")
                if attempt < max_retries - 1:
                    print(f"   ⏳ Waiting 3s before retry...")
                    time.sleep(3)

        except (SSLError, MaxRetryError, ConnectionError, Timeout) as e:
            print(f"   🔄 Network error on attempt {attempt+1}/{max_retries}: {type(e).__name__}")
            if attempt < max_retries - 1:
                print(f"   ⏳ Network issue - waiting 3s before retry...")
                time.sleep(3)
        except Exception as e:
            print(f"   ❌ Unexpected error on attempt {attempt+1}/{max_retries}: {e}")
            if attempt < max_retries - 1:
                print(f"   ⏳ Waiting 3s before retry...")
                time.sleep(3)

    session.close()
    print(f"   ❌ Failed to update collection after {max_retries} attempts")
    return False

def main():
    """Main menu for autoblogger operations"""
    config = load_config()

    print("🚀 MASTER AUTOBLOGGER AUTOMATION")
    print("=" * 50)
    print(f"Store: {config['shopify']['store_address']}")
    print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    while True:
        print("\n📋 Choose an option:")
        print("1. 📝 Create blogs from CSV")
        print("2. 📁 Create collections from CSV")
        print("3. 🔗 Assign products to recent collections")
        print("4. 🎯 Generate SEO meta for recent blogs")
        print("5. 🎯 Generate SEO meta for recent collections")
        print("6. 🧹 Remove duplicate blogs")
        print("7. 🧹 Remove duplicate collections")
        print("8. 🖼️ BULK ADD IMAGES (boost conversions!)")
        print("9. 🚀 FULL AUTOMATION (all steps)")
        print("10. 💡 Show optimization tips")
        print("0. ❌ Exit")

        choice = input("\n🔢 Enter your choice (0-10): ").strip()

        if choice == '1':
            create_blogs_optimized()
        elif choice == '2':
            create_collections_optimized()
        elif choice == '3':
            assign_products_to_recent_collections()
        elif choice == '4':
            generate_seo_meta_recent_blogs()
        elif choice == '5':
            generate_seo_meta_recent_collections()
        elif choice == '6':
            remove_duplicate_blogs()
        elif choice == '7':
            remove_duplicate_collections()
        elif choice == '8':
            bulk_add_images()
        elif choice == '9':
            confirm = input("🚀 Run full automation? This may take a while. (y/n): ").strip().lower()
            if confirm == 'y':
                run_full_automation()
        elif choice == '10':
            show_optimization_tips()
        elif choice == '0':
            print("👋 Goodbye!")
            break
        else:
            print("❌ Invalid choice. Please enter 0-10.")

if __name__ == "__main__":
    main()
