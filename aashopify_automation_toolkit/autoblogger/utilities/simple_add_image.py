import json
import requests
import time
from requests.exceptions import <PERSON><PERSON>rror, ConnectionError, Timeout
from urllib3.exceptions import MaxRetryError

# Load config
def load_config():
    with open('config.json', 'r') as config_file:
        return json.load(config_file)

config = load_config()

# Shopify API credentials
password = config['shopify']['password']
store_address = config['shopify']['store_address']

headers = {
    'Content-Type': 'application/json',
    'X-Shopify-Access-Token': password
}

def get_blogs():
    """Get all blogs"""
    print("🔄 Getting your blogs...")

    # Get blogs
    url = f"{store_address}/api/2023-01/blogs.json"
    response = requests.get(url, headers=headers)
    blogs = response.json().get('blogs', [])

    all_blog_posts = []

    # Get articles from all blogs
    for blog in blogs:
        blog_id = blog['id']
        print(f"📥 Getting articles from blog: {blog['title']}")

        # Get articles with pagination
        page_url = f"{store_address}/api/2023-01/blogs/{blog_id}/articles.json?limit=250"

        while page_url:
            response = requests.get(page_url, headers=headers)
            if response.status_code == 200:
                data = response.json()
                articles = data.get('articles', [])

                for article in articles:
                    all_blog_posts.append({
                        'id': article['id'],
                        'title': article['title'],
                        'body_html': article.get('body_html', ''),
                        'blog_id': blog_id
                    })

                # Check for next page
                link_header = response.headers.get('Link', '')
                if 'rel="next"' in link_header:
                    for link in link_header.split(','):
                        if 'rel="next"' in link:
                            page_url = link.split('<')[1].split('>')[0]
                            break
                    else:
                        page_url = None
                else:
                    page_url = None

                time.sleep(0.5)  # Be nice to API
            else:
                break

    print(f"📊 Found {len(all_blog_posts)} total blog posts")
    return all_blog_posts

def get_collections():
    """Get all collections"""
    print("🔄 Getting your collections...")

    all_collections = []

    # Get custom collections
    url = f"{store_address}/api/2023-01/custom_collections.json?limit=250"
    while url:
        response = requests.get(url, headers=headers)
        if response.status_code == 200:
            data = response.json()
            collections = data.get('custom_collections', [])

            for col in collections:
                all_collections.append({
                    'id': col['id'],
                    'title': col['title'],
                    'body_html': col.get('body_html', ''),
                    'type': 'custom_collections'
                })

            # Check for next page
            link_header = response.headers.get('Link', '')
            if 'rel="next"' in link_header:
                for link in link_header.split(','):
                    if 'rel="next"' in link:
                        url = link.split('<')[1].split('>')[0]
                        break
                else:
                    url = None
            else:
                url = None

            time.sleep(0.5)  # Be nice to API
        else:
            break

    # Get smart collections
    url = f"{store_address}/api/2023-01/smart_collections.json?limit=250"
    while url:
        response = requests.get(url, headers=headers)
        if response.status_code == 200:
            data = response.json()
            collections = data.get('smart_collections', [])

            for col in collections:
                all_collections.append({
                    'id': col['id'],
                    'title': col['title'],
                    'body_html': col.get('body_html', ''),
                    'type': 'smart_collections'
                })

            # Check for next page
            link_header = response.headers.get('Link', '')
            if 'rel="next"' in link_header:
                for link in link_header.split(','):
                    if 'rel="next"' in link:
                        url = link.split('<')[1].split('>')[0]
                        break
                else:
                    url = None
            else:
                url = None

            time.sleep(0.5)  # Be nice to API
        else:
            break

    print(f"📊 Found {len(all_collections)} total collections")
    return all_collections

def add_image_to_content(content, image_url, position="top"):
    """Add image at top or bottom"""
    image_html = f'<img src="{image_url}" style="max-width: 100%; height: auto; margin: 20px auto; display: block;" />'

    if position == "top":
        return image_html + '\n\n' + content
    else:  # bottom
        return content + '\n\n' + image_html

def update_blog(blog_id, article_id, new_content, max_retries=15):
    """Update blog with new content - with robust error handling and retries"""
    url = f"{store_address}/api/2023-01/blogs/{blog_id}/articles/{article_id}.json"

    data = {
        "article": {
            "id": article_id,
            "body_html": new_content
        }
    }

    # Create a session for better connection handling
    session = requests.Session()

    for attempt in range(max_retries):
        try:
            # Use longer timeout and session for better connection handling
            response = session.put(url, json=data, headers=headers, timeout=90)

            if response.status_code == 200:
                session.close()
                return True
            else:
                print(f"   ⚠️ HTTP {response.status_code} on attempt {attempt+1}/{max_retries}")
                if attempt < max_retries - 1:
                    print(f"   ⏳ Waiting 3s before retry...")
                    time.sleep(3)

        except (SSLError, MaxRetryError, ConnectionError, Timeout) as e:
            print(f"   🔄 Network error on attempt {attempt+1}/{max_retries}: {type(e).__name__}")
            if attempt < max_retries - 1:
                print(f"   ⏳ Network issue - waiting 3s before retry...")
                time.sleep(3)
        except Exception as e:
            print(f"   ❌ Unexpected error on attempt {attempt+1}/{max_retries}: {e}")
            if attempt < max_retries - 1:
                print(f"   ⏳ Waiting 3s before retry...")
                time.sleep(3)

    session.close()
    print(f"   ❌ Failed to update blog after {max_retries} attempts")
    return False

def update_collection(collection_id, collection_type, new_content, max_retries=15):
    """Update collection with new content - with robust error handling and retries"""
    url = f"{store_address}/api/2023-01/{collection_type}/{collection_id}.json"

    # Remove 's' from collection type for the data key
    data_key = collection_type.rstrip('s')

    data = {
        data_key: {
            "id": collection_id,
            "body_html": new_content
        }
    }

    # Create a session for better connection handling
    session = requests.Session()

    for attempt in range(max_retries):
        try:
            # Use longer timeout and session for better connection handling
            response = session.put(url, json=data, headers=headers, timeout=90)

            if response.status_code == 200:
                session.close()
                return True
            else:
                print(f"   ⚠️ HTTP {response.status_code} on attempt {attempt+1}/{max_retries}")
                if attempt < max_retries - 1:
                    print(f"   ⏳ Waiting 3s before retry...")
                    time.sleep(3)

        except (SSLError, MaxRetryError, ConnectionError, Timeout) as e:
            print(f"   🔄 Network error on attempt {attempt+1}/{max_retries}: {type(e).__name__}")
            if attempt < max_retries - 1:
                print(f"   ⏳ Network issue - waiting 3s before retry...")
                time.sleep(3)
        except Exception as e:
            print(f"   ❌ Unexpected error on attempt {attempt+1}/{max_retries}: {e}")
            if attempt < max_retries - 1:
                print(f"   ⏳ Waiting 3s before retry...")
                time.sleep(3)

    session.close()
    print(f"   ❌ Failed to update collection after {max_retries} attempts")
    return False

def has_image(content):
    """Check if content already has an image"""
    return '<img' in content.lower() if content else False

def main():
    print("🖼️ BULK IMAGE DROPPER")
    print("=" * 30)
    print("Add the same image to multiple blogs/collections!")

    # Get image URL first
    image_url = input("\n🖼️ Enter your image URL: ").strip()
    if not image_url.startswith(('http://', 'https://')):
        print("❌ Please enter a valid URL starting with http:// or https://")
        return

    print(f"✅ Image URL: {image_url}")

    # Choose what to add image to
    print("\n📋 What do you want to add this image to?")
    print("1. ALL blogs that don't have images")
    print("2. ALL collections that don't have images")
    print("3. ALL blogs (even if they have images)")
    print("4. ALL collections (even if they have images)")

    choice = input("\nChoose (1-4): ").strip()

    # Choose image position
    print("\n📍 Where to place the image?")
    print("1. TOP (above content) - BEST for collections & conversion")
    print("2. BOTTOM (below content) - BEST for blogs & SEO")

    position_choice = input("\nChoose position (1-2): ").strip()
    position = "top" if position_choice == "1" else "bottom"

    position_text = "TOP (above content)" if position == "top" else "BOTTOM (below content)"
    print(f"✅ Position: {position_text}")

    if choice in ["1", "3"]:
        # Bulk add to blogs
        blogs = get_blogs()
        if not blogs:
            print("❌ No blogs found!")
            return

        if choice == "1":
            # Only blogs without images
            target_blogs = [blog for blog in blogs if not has_image(blog['body_html'])]
            print(f"📊 Found {len(target_blogs)} blogs WITHOUT images")
        else:
            # All blogs
            target_blogs = blogs
            print(f"📊 Found {len(target_blogs)} total blogs")

        if not target_blogs:
            print("✅ All blogs already have images!")
            return

        # Confirm
        confirm = input(f"\n✅ Add image to {len(target_blogs)} blogs? (y/n): ").strip().lower()
        if confirm != 'y':
            print("❌ Cancelled")
            return

        # Add images
        print(f"\n🔄 Adding images to {len(target_blogs)} blogs...")
        success = 0

        for i, blog in enumerate(target_blogs, 1):
            print(f"📝 {i}/{len(target_blogs)}: {blog['title'][:50]}...")

            new_content = add_image_to_content(blog['body_html'], image_url, position)
            if update_blog(blog['blog_id'], blog['id'], new_content):
                success += 1
                print("   ✅ Done")
            else:
                print("   ❌ Failed")

            time.sleep(1)  # Be nice to API

        print(f"\n🎉 Successfully added images to {success}/{len(target_blogs)} blogs!")

    elif choice in ["2", "4"]:
        # Bulk add to collections
        collections = get_collections()
        if not collections:
            print("❌ No collections found!")
            return

        if choice == "2":
            # Only collections without images
            target_collections = [col for col in collections if not has_image(col['body_html'])]
            print(f"📊 Found {len(target_collections)} collections WITHOUT images")
        else:
            # All collections
            target_collections = collections
            print(f"📊 Found {len(target_collections)} total collections")

        if not target_collections:
            print("✅ All collections already have images!")
            return

        # Confirm
        confirm = input(f"\n✅ Add image to {len(target_collections)} collections? (y/n): ").strip().lower()
        if confirm != 'y':
            print("❌ Cancelled")
            return

        # Add images
        print(f"\n🔄 Adding images to {len(target_collections)} collections...")
        success = 0

        for i, col in enumerate(target_collections, 1):
            print(f"🏷️ {i}/{len(target_collections)}: {col['title'][:50]}...")

            new_content = add_image_to_content(col['body_html'], image_url, position)
            if update_collection(col['id'], col['type'], new_content):
                success += 1
                print("   ✅ Done")
            else:
                print("   ❌ Failed")

            time.sleep(1)  # Be nice to API

        print(f"\n🎉 Successfully added images to {success}/{len(target_collections)} collections!")

    else:
        print("❌ Please choose 1, 2, 3, or 4")

if __name__ == "__main__":
    main()
