#!/usr/bin/env python3
"""
Automated script to remove duplicate blog articles from a Shopify store without manual confirmation.
Duplicate blogs are identified by their handles (URLs) ending with -1, -2, -3, etc.
This script can be scheduled to run periodically.
"""

import json
import os
import re
import requests
import time
import logging
import argparse
from datetime import datetime
from requests.exceptions import SSLError, RequestException
from urllib3.exceptions import MaxRetryError
from tenacity import retry, wait_random_exponential, stop_after_attempt

# Set up logging to console only
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# Load the JSON config file
def load_config():
    with open('../config.json', 'r') as config_file:
        return json.load(config_file)

config = load_config()

# Shopify API credentials
api_key = config['shopify']['api_key']
password = config['shopify']['password']
store_address = config['shopify']['store_address']
blog_id = config['shopify']['blog_id']

# Headers for API requests
headers = {
    "Content-Type": "application/json",
    "Accept": "application/json"
}

# Regular expression to identify duplicate blogs
# Matches handles (URLs) ending with -1, -2, -3, -4, -5, -6, -7, -8, -9 (single digits only)
# This pattern specifically looks for single digit suffixes that Shopify adds for duplicates
DUPLICATE_PATTERN = re.compile(r'-[1-9]$')  # Matches only -1 to -9, excludes years like -2025

@retry(wait=wait_random_exponential(min=1, max=10), stop=stop_after_attempt(5))
def get_all_blog_articles(max_retries=5):
    """
    Fetch all blog articles from the Shopify store.
    Returns a list of article objects.
    """
    articles = []
    page_info = None

    while True:
        url = f"{store_address}/blogs/{blog_id}/articles.json?limit=250"
        if page_info:
            url += f"&page_info={page_info}"

        for attempt in range(max_retries):
            try:
                response = requests.get(url, headers=headers, auth=(api_key, password))
                if response.status_code == 200:
                    data = response.json()
                    articles.extend(data.get('articles', []))

                    # Check for pagination
                    link_header = response.headers.get('Link', '')
                    if 'rel="next"' in link_header:
                        # Extract page_info from the Link header
                        next_link = [link for link in link_header.split(',') if 'rel="next"' in link][0]
                        page_info = next_link.split('page_info=')[1].split('&')[0].split('>')[0]
                    else:
                        # No more pages
                        return articles
                else:
                    logger.error(f"Failed to fetch blog articles: {response.status_code} - {response.text}")
                    if attempt == max_retries - 1:
                        return articles
                    time.sleep(1)  # Wait before retrying
                break
            except (SSLError, MaxRetryError, RequestException) as e:
                logger.error(f"Error on attempt {attempt+1}/{max_retries}: {e}")
                if attempt == max_retries - 1:
                    return articles
                time.sleep(1)  # Wait before retrying

    return articles

@retry(wait=wait_random_exponential(min=1, max=10), stop=stop_after_attempt(5))
def delete_blog_article(article_id, max_retries=5):
    """
    Delete a blog article from the Shopify store.
    Returns True if successful, False otherwise.
    """
    url = f"{store_address}/blogs/{blog_id}/articles/{article_id}.json"

    for attempt in range(max_retries):
        try:
            response = requests.delete(url, headers=headers, auth=(api_key, password))
            if response.status_code == 200:
                return True
            else:
                logger.error(f"Failed to delete blog article {article_id}: {response.status_code} - {response.text}")
                if attempt == max_retries - 1:
                    return False
                time.sleep(1)  # Wait before retrying
        except (SSLError, MaxRetryError, RequestException) as e:
            logger.error(f"Error on attempt {attempt+1}/{max_retries}: {e}")
            if attempt == max_retries - 1:
                return False
            time.sleep(1)  # Wait before retrying

    return False

def identify_duplicate_blogs(articles):
    """
    Identify duplicate blog articles based on their handles (URLs).
    A blog article is considered a duplicate if:
    1. Its handle ends with a single digit (like -1, -2, -3, -4, -5, -6, -7, -8, -9)
    2. There exists another article with the same handle but without the suffix
    Returns a list of articles that are considered duplicates.
    """
    duplicates = []
    handle_set = {article.get('handle', '') for article in articles}

    for article in articles:
        handle = article.get('handle', '')
        match = DUPLICATE_PATTERN.search(handle)
        if match:
            # Extract the base handle (without the -1, -2, etc. suffix)
            base_handle = handle[:match.start()]

            # Check if there's an article with the base handle
            if base_handle in handle_set:
                duplicates.append(article)
                logger.info(f"Found duplicate: {article.get('title')} (handle: {handle}) - base exists: {base_handle}")
            else:
                logger.info(f"Skipping potential duplicate: {article.get('title')} (handle: {handle}) - no base article found")

    return duplicates

def main():
    # Set up command line arguments
    parser = argparse.ArgumentParser(description='Automatically remove duplicate Shopify blog articles.')
    parser.add_argument('--limit', type=int, help='Limit the number of articles to delete')
    parser.add_argument('--batch', type=int, default=10, help='Process articles in batches of this size (default: 10)')
    parser.add_argument('--dry-run', action='store_true', help='Only identify duplicates without deleting them')
    args = parser.parse_args()

    logger.info("Starting automated duplicate blog article removal process...")

    # Get all blog articles
    logger.info("Fetching all blog articles from your Shopify store...")
    all_articles = get_all_blog_articles()
    logger.info(f"Found {len(all_articles)} blog articles in total.")

    # Identify duplicates
    duplicate_articles = identify_duplicate_blogs(all_articles)
    logger.info(f"Identified {len(duplicate_articles)} duplicate blog articles.")

    if not duplicate_articles:
        logger.info("No duplicate blog articles found. Nothing to remove.")
        return

    # Apply limit if specified
    if args.limit and args.limit > 0 and args.limit < len(duplicate_articles):
        logger.info(f"Limiting to {args.limit} articles as requested.")
        duplicate_articles = duplicate_articles[:args.limit]

    # Log duplicates
    logger.info("Duplicate blog articles to be removed:")
    for i, article in enumerate(duplicate_articles, 1):
        logger.info(f"{i}. ID: {article['id']} - Title: {article['title']} - Handle: {article['handle']}")

    # Exit if this is a dry run
    if args.dry_run:
        logger.info("Dry run mode - no articles will be deleted.")
        return

    # Delete duplicates
    logger.info("Deleting duplicate blog articles...")
    deleted_count = 0

    # Process in batches
    batch_size = args.batch
    total_batches = (len(duplicate_articles) + batch_size - 1) // batch_size

    for batch_num in range(total_batches):
        start_idx = batch_num * batch_size
        end_idx = min((batch_num + 1) * batch_size, len(duplicate_articles))
        batch = duplicate_articles[start_idx:end_idx]

        logger.info(f"Processing batch {batch_num + 1}/{total_batches} ({len(batch)} articles)...")

        for article in batch:
            article_id = article['id']
            title = article['title']
            handle = article['handle']
            logger.info(f"Deleting article: {title} (ID: {article_id}, Handle: {handle})...")

            if delete_blog_article(article_id):
                logger.info(f"✅ Successfully deleted article: {title}")
                deleted_count += 1
            else:
                logger.error(f"❌ Failed to delete article: {title}")

        # Add a small delay between batches to avoid API rate limits
        if batch_num < total_batches - 1:
            logger.info(f"Batch {batch_num + 1} complete. Pausing briefly before next batch...")
            time.sleep(2)

    logger.info(f"Deletion complete. Removed {deleted_count} out of {len(duplicate_articles)} duplicate blog articles.")

if __name__ == "__main__":
    main()
