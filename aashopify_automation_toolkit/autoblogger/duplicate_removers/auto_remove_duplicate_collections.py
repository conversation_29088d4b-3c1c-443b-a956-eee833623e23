#!/usr/bin/env python3
"""
Automated script to remove duplicate collections from a Shopify store without manual confirmation.
Duplicate collections are identified by their handles (URLs) ending with -1, -2, -3, etc.
This script can be scheduled to run periodically.
"""

import json
import os
import re
import requests
import time
import logging
from datetime import datetime
from requests.exceptions import SSLError, RequestException
from urllib3.exceptions import MaxRetryError
from tenacity import retry, wait_random_exponential, stop_after_attempt

# Set up logging to console only
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# Load the JSON config file
def load_config():
    with open('../config.json', 'r') as config_file:
        return json.load(config_file)

config = load_config()

# Shopify API credentials
password = config['shopify']['password']
store_address = config['shopify']['store_address']

# Headers for API requests
headers = {
    "Content-Type": "application/json",
    "X-Shopify-Access-Token": password
}

# Regular expression to identify duplicate collections
# Matches handles (URLs) ending with -1, -2, -3, -4, -5, -6, -7, -8, etc. up to -20
# This pattern specifically looks for numeric suffixes that Shopify adds for duplicates
DUPLICATE_PATTERN = re.compile(r'-([1-9]|[12][0-9]|30)$')  # Matches -1 to -30, excludes years like -2025

@retry(wait=wait_random_exponential(min=1, max=10), stop=stop_after_attempt(5))
def get_all_collections(max_retries=5):
    """
    Fetch all collections from the Shopify store.
    Returns a list of collection objects.
    """
    collections = []
    page_info = None

    while True:
        url = f"{store_address}/api/2023-01/custom_collections.json?limit=250"
        if page_info:
            url += f"&page_info={page_info}"

        for attempt in range(max_retries):
            try:
                response = requests.get(url, headers=headers)
                if response.status_code == 200:
                    data = response.json()
                    collections.extend(data.get('custom_collections', []))

                    # Check for pagination
                    link_header = response.headers.get('Link', '')
                    if 'rel="next"' in link_header:
                        # Extract page_info from the Link header
                        next_link = [link for link in link_header.split(',') if 'rel="next"' in link][0]
                        page_info = next_link.split('page_info=')[1].split('&')[0].split('>')[0]
                    else:
                        # No more pages
                        return collections
                else:
                    logger.error(f"Failed to fetch collections: {response.status_code} - {response.text}")
                    if attempt == max_retries - 1:
                        return collections
                    time.sleep(1)  # Wait before retrying
                break
            except (SSLError, MaxRetryError, RequestException) as e:
                logger.error(f"Error on attempt {attempt+1}/{max_retries}: {e}")
                if attempt == max_retries - 1:
                    return collections
                time.sleep(1)  # Wait before retrying

    return collections

@retry(wait=wait_random_exponential(min=1, max=10), stop=stop_after_attempt(5))
def delete_collection(collection_id, max_retries=5):
    """
    Delete a collection from the Shopify store.
    Returns True if successful, False otherwise.
    """
    url = f"{store_address}/api/2023-01/custom_collections/{collection_id}.json"

    for attempt in range(max_retries):
        try:
            response = requests.delete(url, headers=headers)
            if response.status_code == 200:
                return True
            else:
                logger.error(f"Failed to delete collection {collection_id}: {response.status_code} - {response.text}")
                if attempt == max_retries - 1:
                    return False
                time.sleep(1)  # Wait before retrying
        except (SSLError, MaxRetryError, RequestException) as e:
            logger.error(f"Error on attempt {attempt+1}/{max_retries}: {e}")
            if attempt == max_retries - 1:
                return False
            time.sleep(1)  # Wait before retrying

    return False

def identify_duplicate_collections(collections):
    """
    Identify duplicate collections based on their handles (URLs).
    A collection is considered a duplicate if:
    1. Its handle ends with a number from -1 to -30
    Returns a list of collections that are considered duplicates.
    """
    duplicates = []

    for collection in collections:
        handle = collection.get('handle', '')
        match = DUPLICATE_PATTERN.search(handle)
        if match:
            # Any collection ending with -1 to -30 is considered a duplicate
            duplicates.append(collection)
            logger.info(f"Found duplicate: {collection.get('title')} (handle: {handle}) - ends with number suffix")

    return duplicates

def main():
    import argparse

    # Set up command line arguments
    parser = argparse.ArgumentParser(description='Automatically remove duplicate Shopify collections.')
    parser.add_argument('--limit', type=int, help='Limit the number of collections to delete')
    parser.add_argument('--batch', type=int, default=10, help='Process collections in batches of this size (default: 10)')
    parser.add_argument('--dry-run', action='store_true', help='Only identify duplicates without deleting them')
    args = parser.parse_args()

    logger.info("Starting automated duplicate collection removal process...")

    # Get all collections
    logger.info("Fetching all collections from your Shopify store...")
    all_collections = get_all_collections()
    logger.info(f"Found {len(all_collections)} collections in total.")

    # Identify duplicates
    duplicate_collections = identify_duplicate_collections(all_collections)
    logger.info(f"Identified {len(duplicate_collections)} duplicate collections.")

    if not duplicate_collections:
        logger.info("No duplicate collections found. Nothing to remove.")
        return

    # Apply limit if specified
    if args.limit and args.limit > 0 and args.limit < len(duplicate_collections):
        logger.info(f"Limiting to {args.limit} collections as requested.")
        duplicate_collections = duplicate_collections[:args.limit]

    # Log duplicates
    logger.info("Duplicate collections to be removed:")
    for i, collection in enumerate(duplicate_collections, 1):
        logger.info(f"{i}. ID: {collection['id']} - Title: {collection['title']} - Handle: {collection['handle']}")

    # Exit if this is a dry run
    if args.dry_run:
        logger.info("Dry run mode - no collections will be deleted.")
        return

    # Delete duplicates
    logger.info("Deleting duplicate collections...")
    deleted_count = 0

    # Process in batches
    batch_size = args.batch
    total_batches = (len(duplicate_collections) + batch_size - 1) // batch_size

    for batch_num in range(total_batches):
        start_idx = batch_num * batch_size
        end_idx = min((batch_num + 1) * batch_size, len(duplicate_collections))
        batch = duplicate_collections[start_idx:end_idx]

        logger.info(f"Processing batch {batch_num + 1}/{total_batches} ({len(batch)} collections)...")

        for collection in batch:
            collection_id = collection['id']
            title = collection['title']
            handle = collection['handle']
            logger.info(f"Deleting collection: {title} (ID: {collection_id}, Handle: {handle})...")

            if delete_collection(collection_id):
                logger.info(f"✅ Successfully deleted collection: {title}")
                deleted_count += 1
            else:
                logger.error(f"❌ Failed to delete collection: {title}")

        # Add a small delay between batches to avoid API rate limits
        if batch_num < total_batches - 1:
            logger.info(f"Batch {batch_num + 1} complete. Pausing briefly before next batch...")
            time.sleep(2)

    logger.info(f"Deletion complete. Removed {deleted_count} out of {len(duplicate_collections)} duplicate collections.")

if __name__ == "__main__":
    main()
