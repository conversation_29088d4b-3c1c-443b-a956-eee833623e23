# Shopify Duplicate Content Removal Tools

This directory contains tools to help you remove duplicate content from your Shopify store. Currently, it includes tools for removing duplicate collections and blog articles.

## Available Tools

### Collection Duplicate Removers

These tools help you identify and remove duplicate collections from your Shopify store. Duplicate collections are identified by their handles (URLs) ending with `-1`, `-2`, `-3`, etc.

#### 1. Interactive Collection Removal Tool (`remove_duplicate_collections.py`)

This script provides an interactive way to remove duplicate collections. It will:
- Fetch all collections from your Shopify store
- Identify duplicates (collections with handles/URLs ending in `-1`, `-2`, `-3`, etc.)
- Display the list of duplicates
- Ask for confirmation before deleting
- Delete the confirmed duplicates
- Report the results

##### Usage:

```bash
python remove_duplicate_collections.py [options]
```

##### Options:

- `--limit NUMBER`: Limit the number of collections to delete
- `--batch NUMBER`: Process collections in batches of this size
- `--auto`: Run in automatic mode without confirmation prompts

#### 2. Automated Collection Removal Tool (`auto_remove_duplicate_collections.py`)

This script automatically removes duplicate collections without requiring manual confirmation. It's suitable for scheduled tasks or batch processing. The script will:
- Fetch all collections from your Shopify store
- Identify duplicates (collections with handles/URLs ending in `-1`, `-2`, `-3`, etc.)
- Delete all identified duplicates
- Log all actions to both console and a log file

##### Usage:

```bash
python auto_remove_duplicate_collections.py [options]
```

##### Options:

- `--limit NUMBER`: Limit the number of collections to delete
- `--batch NUMBER`: Process collections in batches of this size (default: 10)
- `--dry-run`: Only identify duplicates without deleting them

### Blog Duplicate Removers

These tools help you identify and remove duplicate blog articles from your Shopify store. Duplicate blog articles are identified by their handles (URLs) ending with `-1`, `-2`, `-3`, etc.

#### 1. Interactive Blog Removal Tool (`remove_duplicate_blogs.py`)

This script provides an interactive way to remove duplicate blog articles. It will:
- Fetch all blog articles from your Shopify store
- Identify duplicates (articles with handles/URLs ending in `-1`, `-2`, `-3`, etc.)
- Display the list of duplicates
- Ask for confirmation before deleting
- Delete the confirmed duplicates
- Report the results

##### Usage:

```bash
python remove_duplicate_blogs.py [options]
```

##### Options:

- `--limit NUMBER`: Limit the number of articles to delete
- `--batch NUMBER`: Process articles in batches of this size
- `--auto`: Run in automatic mode without confirmation prompts

#### 2. Automated Blog Removal Tool (`auto_remove_duplicate_blogs.py`)

This script automatically removes duplicate blog articles without requiring manual confirmation. It's suitable for scheduled tasks or batch processing. The script will:
- Fetch all blog articles from your Shopify store
- Identify duplicates (articles with handles/URLs ending in `-1`, `-2`, `-3`, etc.)
- Delete all identified duplicates
- Log all actions to both console and a log file

##### Usage:

```bash
python auto_remove_duplicate_blogs.py [options]
```

##### Options:

- `--limit NUMBER`: Limit the number of articles to delete
- `--batch NUMBER`: Process articles in batches of this size (default: 10)
- `--dry-run`: Only identify duplicates without deleting them

## Setting Up as a Scheduled Task

### On Linux/Mac (using cron):

1. Open your crontab file:
   ```bash
   crontab -e
   ```

2. Add lines to run the scripts daily at specific times:
   ```
   # Remove duplicate collections at 2 AM
   0 2 * * * cd /path/to/autoblogger && python duplicate_removers/auto_remove_duplicate_collections.py --batch 10
   
   # Remove duplicate blogs at 3 AM
   0 3 * * * cd /path/to/autoblogger && python duplicate_removers/auto_remove_duplicate_blogs.py --batch 10
   ```

### On Windows (using Task Scheduler):

1. Open Task Scheduler
2. Create a new Basic Task for each script
3. Set the trigger (e.g., daily at 2 AM for collections, 3 AM for blogs)
4. Set the action to "Start a program"
5. Program/script: `python`
6. Add arguments: `duplicate_removers/auto_remove_duplicate_collections.py --batch 10` (or the blog version)
7. Start in: `C:\path\to\autoblogger`

## Troubleshooting

If you encounter any issues:

1. Check the log files in the `logs` directory
2. Verify your Shopify API credentials in the `config.json` file
3. Ensure you have the required Python packages installed:
   ```bash
   pip install requests tenacity
   ```

## Security Note

These scripts use your Shopify API credentials from the `config.json` file. Make sure this file is kept secure and not shared publicly.
