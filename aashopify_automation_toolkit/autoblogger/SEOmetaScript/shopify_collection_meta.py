import g4f
import json
import os
import traceback
import csv
import re
import requests
import time
import datetime
from datetime import datetime as dt, timedelta, timezone
from tqdm import tqdm
from concurrent.futures import ThreadPoolExecutor, as_completed
from requests.exceptions import SSLError
from urllib3.exceptions import MaxRetryError

# Load the JSON config file
def load_config():
    try:
        with open('../config.json', 'r') as config_file:
            return json.load(config_file)
    except FileNotFoundError:
        print("❌ Config file not found. Please make sure config.json exists.")
        exit(1)

config = load_config()

# Shopify API credentials
password = config['shopify']['password']
store_address = config['shopify']['store_address']
product_name = config['shopify']['product_name']
brand_name = config['shopify']['brandname']
competitor_name = config['shopify']['competitor_website']

# API headers
headers = {
    "Content-Type": "application/json",
    "X-Shopify-Access-Token": password
}

def generate_chat_completion(messages, max_retries=5, use_fast_model=False):
    """Generate chat completion with PollinationsAI priority"""
    model = 'gpt-4o-mini' if use_fast_model else 'gpt-4o'
    print(f"Using model: {model}")

    # Try PollinationsAI first
    for attempt in range(max_retries):
        try:
            response = g4f.ChatCompletion.create(
                model=model,
                messages=messages,
                provider=g4f.Provider.PollinationsAI,
                timeout=60
            )
            if response and len(str(response).strip()) > 10:
                return response
        except Exception as e:
            print(f"PollinationsAI attempt {attempt+1}/{max_retries} failed: {str(e)[:50]}...")
            if attempt < max_retries - 1:
                time.sleep(2)

    # Fallback to auto-selection
    for attempt in range(max_retries):
        try:
            response = g4f.ChatCompletion.create(
                model=model,
                messages=messages,
                timeout=60
            )
            if response and len(str(response).strip()) > 10:
                print("✅ Success with auto-selection")
                return response
        except Exception as e:
            print(f"Auto-selection attempt {attempt+1}/{max_retries} failed: {str(e)[:50]}...")
            if attempt < max_retries - 1:
                time.sleep(2)

    print(f"Failed to generate completion after {max_retries} attempts")
    return None

def fetch_collections_from_shopify(max_retries=5):
    """Fetch ALL collections from Shopify API with pagination"""
    collections = []
    total_collections = 0

    def fetch_collection_type(collection_type):
        nonlocal total_collections
        print(f"Fetching {collection_type}...")
        url = f"{store_address}/api/2023-01/{collection_type}.json?limit=250"
        page_count = 0

        while url:
            page_count += 1
            print(f"Fetching page {page_count} of {collection_type}...")

            for attempt in range(max_retries):
                try:
                    response = requests.get(url, headers=headers, timeout=30)
                    if response.status_code == 200:
                        data = response.json()
                        page_collections = data.get(collection_type, [])
                        print(f"Found {len(page_collections)} {collection_type} on page {page_count}")

                        for collection in page_collections:
                            collections.append({
                                'id': collection['id'],
                                'name': collection['title'],
                                'handle': collection['handle'],
                                'description': collection.get('body_html', ''),
                                'type': collection_type[:-1]  # removes the 's'
                            })
                            total_collections += 1

                        # Pagination: look for 'Link' header with rel="next"
                        link = response.headers.get('Link')
                        if link and 'rel="next"' in link:
                            # Extract next page URL from Link header
                            next_url = None
                            for part in link.split(','):
                                if 'rel="next"' in part:
                                    next_url = part.split(';')[0].strip('<> ')
                                    break
                            url = next_url
                            # Add a small delay between pagination requests
                            if url:
                                time.sleep(0.5)
                        else:
                            url = None
                        break
                    else:
                        print(f"❌ Failed to fetch {collection_type} page {page_count}: {response.status_code} - {response.text}")
                        if attempt < max_retries - 1:
                            time.sleep(2)
                except Exception as e:
                    print(f"Error on attempt {attempt+1}/{max_retries} while fetching {collection_type} page {page_count}: {e}")
                    if attempt < max_retries - 1:
                        time.sleep(2)
            else:
                print(f"Failed to fetch {collection_type} page {page_count} after {max_retries} attempts, moving on...")
                url = None

    print("Starting collection fetch process...")
    fetch_collection_type('custom_collections')
    fetch_collection_type('smart_collections')
    print(f"Total collections fetched: {total_collections}")
    return collections

def fetch_recent_collections_from_shopify(hours=24, max_retries=5):
    """Fetch collections created in the last specified hours from Shopify API"""
    print(f"🔄 Fetching collections created in the last {hours} hours...")

    # Calculate the datetime cutoff
    cutoff_time = dt.now(timezone.utc) - timedelta(hours=hours)
    cutoff_time_str = cutoff_time.strftime('%Y-%m-%dT%H:%M:%S+00:00')
    print(f"🕒 Looking for collections created after: {cutoff_time.strftime('%Y-%m-%d %H:%M:%S UTC')}")

    collections = []
    total_collections = 0

    def fetch_recent_collection_type(collection_type):
        nonlocal total_collections
        print(f"Fetching recent {collection_type}...")

        # Use created_at_min filter to get only recent collections
        url = f"{store_address}/api/2023-01/{collection_type}.json?limit=250&created_at_min={cutoff_time_str}"
        page_count = 0

        while url:
            page_count += 1
            print(f"Fetching page {page_count} of recent {collection_type}...")

            for attempt in range(max_retries):
                try:
                    response = requests.get(url, headers=headers, timeout=30)
                    if response.status_code == 200:
                        data = response.json()
                        page_collections = data.get(collection_type, [])
                        print(f"Found {len(page_collections)} {collection_type} on page {page_count}")

                        # Additional client-side filtering to ensure we only get recent collections
                        for collection in page_collections:
                            # Try created_at first, then published_at as fallback
                            date_field = None
                            date_value = None

                            if 'created_at' in collection and collection['created_at']:
                                date_field = 'created_at'
                                date_value = collection['created_at']
                            elif 'published_at' in collection and collection['published_at']:
                                date_field = 'published_at'
                                date_value = collection['published_at']

                            if date_value:
                                try:
                                    # Parse the timestamp
                                    date_str = date_value
                                    # Handle different timestamp formats
                                    if date_str.endswith('Z'):
                                        date_str = date_str[:-1] + '+00:00'
                                    elif '+' not in date_str and 'T' in date_str:
                                        date_str += '+00:00'

                                    parsed_date = dt.fromisoformat(date_str)

                                    # Make sure both datetimes are timezone-aware for comparison
                                    if parsed_date.tzinfo is None:
                                        parsed_date = parsed_date.replace(tzinfo=timezone.utc)

                                    if parsed_date >= cutoff_time:
                                        collection_data = {
                                            'id': collection['id'],
                                            'name': collection['title'],
                                            'handle': collection['handle'],
                                            'description': collection.get('body_html', ''),
                                            'type': collection_type[:-1],  # removes the 's'
                                            '_date_used': date_field,
                                            '_creation_date': parsed_date
                                        }
                                        collections.append(collection_data)
                                        total_collections += 1
                                        print(f"   ✅ Recent: {collection['title']} ({date_field}: {parsed_date.strftime('%Y-%m-%d %H:%M:%S UTC')})")

                                except (ValueError, TypeError) as e:
                                    print(f"⚠️  Could not parse {date_field} for collection '{collection.get('title', 'Unknown')}': {e}")
                                    continue
                            else:
                                # If no date available, include it (fallback)
                                collection_data = {
                                    'id': collection['id'],
                                    'name': collection['title'],
                                    'handle': collection['handle'],
                                    'description': collection.get('body_html', ''),
                                    'type': collection_type[:-1],  # removes the 's'
                                    '_date_used': 'none',
                                    '_creation_date': None
                                }
                                collections.append(collection_data)
                                total_collections += 1

                        # Pagination: look for 'Link' header with rel="next"
                        link = response.headers.get('Link')
                        if link and 'rel="next"' in link:
                            # Extract next page URL from Link header
                            next_url = None
                            for part in link.split(','):
                                if 'rel="next"' in part:
                                    next_url = part.split(';')[0].strip('<> ')
                                    break
                            url = next_url
                            # Add a small delay between pagination requests
                            if url:
                                time.sleep(0.5)
                        else:
                            url = None
                        break
                    else:
                        print(f"❌ Failed to fetch {collection_type} page {page_count}: {response.status_code} - {response.text}")
                        if attempt < max_retries - 1:
                            time.sleep(2)
                except Exception as e:
                    print(f"Error on attempt {attempt+1}/{max_retries} while fetching {collection_type} page {page_count}: {e}")
                    if attempt < max_retries - 1:
                        time.sleep(2)
            else:
                print(f"Failed to fetch {collection_type} page {page_count} after {max_retries} attempts, moving on...")
                url = None

    print("Starting recent collection fetch process...")
    fetch_recent_collection_type('custom_collections')
    fetch_recent_collection_type('smart_collections')
    print(f"🆕 Recent collections found: {total_collections}")
    return collections

def debug_collection_data(max_retries=5):
    """Debug function to show sample collection data structure"""
    print("🔍 Fetching sample collections to debug data structure...")

    def debug_collection_type(collection_type):
        print(f"📊 Fetching sample {collection_type}...")
        url = f"{store_address}/api/2023-01/{collection_type}.json?limit=3"

        for attempt in range(max_retries):
            try:
                response = requests.get(url, headers=headers, timeout=30)
                if response.status_code == 200:
                    data = response.json()
                    collections = data.get(collection_type, [])

                    print(f"📊 Sample of {len(collections)} {collection_type}:")
                    for i, collection in enumerate(collections):
                        print(f"\n🔸 Collection {i+1}:")
                        print(f"   ID: {collection.get('id', 'N/A')}")
                        print(f"   Title: {collection.get('title', 'N/A')}")
                        print(f"   Handle: {collection.get('handle', 'N/A')}")
                        print(f"   Created At: {collection.get('created_at', 'N/A')}")
                        print(f"   Published At: {collection.get('published_at', 'N/A')}")
                        print(f"   Updated At: {collection.get('updated_at', 'N/A')}")
                        print(f"   Available fields: {list(collection.keys())}")

                    return collections
                else:
                    print(f"❌ Failed to fetch {collection_type}: {response.status_code}")
                    if attempt < max_retries - 1:
                        time.sleep(2)
            except Exception as e:
                print(f"Error fetching {collection_type} (attempt {attempt+1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(2)

        print(f"❌ Failed to fetch sample {collection_type}")
        return []

    # Debug both collection types
    debug_collection_type('custom_collections')
    debug_collection_type('smart_collections')

def update_collection_seo(collection_id, collection_type, meta_title, meta_description, max_retries=5):
    """Update collection SEO meta fields"""
    url = f"{store_address}/api/2023-01/{collection_type}s/{collection_id}.json"

    payload = {
        collection_type: {
            "id": collection_id,
            "metafields": [
                {
                    "namespace": "global",
                    "key": "title_tag",
                    "value": meta_title,
                    "type": "single_line_text_field"
                },
                {
                    "namespace": "global",
                    "key": "description_tag",
                    "value": meta_description,
                    "type": "multi_line_text_field"
                }
            ]
        }
    }

    for attempt in range(max_retries):
        try:
            response = requests.put(url, headers=headers, json=payload)

            if response.status_code == 200:
                print(f"✅ Updated SEO for {collection_type} {collection_id}")
                return True
            else:
                print(f"❌ Failed to update SEO for {collection_type} {collection_id}: {response.status_code} - {response.text}")
                if attempt < max_retries - 1:
                    time.sleep(2)  # Wait before retrying
        except (SSLError, MaxRetryError) as e:
            print(f"SSL error on attempt {attempt+1}/{max_retries} while updating collection: {e}")
            if attempt < max_retries - 1:
                time.sleep(2)  # Wait before retrying

    print(f"Failed to update SEO for {collection_type} {collection_id} after {max_retries} attempts")
    return False

def generate_meta_data(collection, max_retries=5, use_fast_model=False):
    """Generate SEO meta title and description for a collection"""
    try:
        collection_name = collection['name']
        description = collection['description']
        collection_id = collection['id']
        collection_type = collection['type']
        handle = collection['handle']

        print(f"🧠 Generating SEO meta data for: {collection_name}")

        messages = [
            {"role": "system", "content": f'''
                You are an SEO expert specializing in e-commerce meta titles and descriptions.
                Your task is to create highly optimized SEO meta titles and descriptions for Shopify collections.

                IMPORTANT: You are creating the SEO meta title and meta description that appear in the "Search engine listing" section of Shopify.
                These are NOT replacing the actual collection title or description.

                The product being sold is: {product_name}
                The brand name is: {brand_name}
                The is my competitor if you wanna use something from him: {competitor_name} (use for inspiration and benchmarking, keywords, if helpful)

                Guidelines:
                - Meta titles should be 50-70 characters for optimal display in search results
                - Meta descriptions should be 150-160 characters max for optimal display
                - Include relevant keywords that will drive sales
                - Create compelling, action-oriented content that encourages clicks
                - Focus on benefits and unique selling points
                - Include a clear call to action in the description
                - Maintain the core message from the original collection title and description
                - Optimize for high conversion, targeting customers ready to purchase
                - Focus on customers who will make fast purchasing decisions

                Format your response as a JSON object with two fields:
                - meta_title: The optimized SEO meta title (for the "Page title" field in Shopify's Search engine listing section)
                - meta_description: The optimized SEO meta description (for the "Meta description" field in Shopify's Search engine listing section)
            '''},
            {"role": "user", "content": f'''
                Please create an optimized SEO meta title and meta description for this collection:

                Collection Name: {collection_name}
                Description: {description}

                These are for the SEO fields in the "Search engine listing" section, NOT to replace the actual collection title or description.

                Return only the JSON object with meta_title and meta_description fields.
            '''}
        ]

        meta_data = generate_chat_completion(messages, max_retries, use_fast_model)

        if not meta_data:
            print(f"❌ Failed to generate meta data for {collection_name}")
            return {
                'Collection ID': collection_id,
                'Collection Name': collection_name,
                'Handle': handle,
                'Description': description,
                'Meta Title': '',
                'Meta Description': '',
                'Status': 'Failed - No response from AI'
            }

        # Parse the JSON response
        try:
            # Clean up the response to ensure it's valid JSON
            cleaned_response = re.search(r'\{.*\}', meta_data, re.DOTALL)
            if cleaned_response:
                meta_data = cleaned_response.group(0)

            meta_data_json = json.loads(meta_data)
            meta_title = meta_data_json.get('meta_title', '')
            meta_description = meta_data_json.get('meta_description', '')

            # Update the collection in Shopify
            update_success = update_collection_seo(collection_id, collection_type, meta_title, meta_description)

            status = "Updated" if update_success else "Failed to update"

            return {
                'Collection ID': collection_id,
                'Collection Name': collection_name,
                'Handle': handle,
                'Description': description,
                'Meta Title': meta_title,
                'Meta Description': meta_description,
                'Status': status
            }
        except json.JSONDecodeError:
            # If the response isn't valid JSON, try to extract the data using string parsing
            print(f"⚠️ JSON parsing failed for {collection_name}, attempting string extraction")

            meta_title = ""
            meta_description = ""

            if "meta_title" in meta_data:
                meta_title_start = meta_data.find("meta_title") + len("meta_title") + 2
                meta_title_end = meta_data.find('"', meta_title_start + 1)
                if meta_title_end > meta_title_start:
                    meta_title = meta_data[meta_title_start:meta_title_end].strip()

            if "meta_description" in meta_data:
                meta_desc_start = meta_data.find("meta_description") + len("meta_description") + 2
                meta_desc_end = meta_data.find('"', meta_desc_start + 1)
                if meta_desc_end > meta_desc_start:
                    meta_description = meta_data[meta_desc_start:meta_desc_end].strip()

            # Update the collection in Shopify
            update_success = update_collection_seo(collection_id, collection_type, meta_title, meta_description)

            status = "Updated (parsed)" if update_success else "Failed to update"

            return {
                'Collection ID': collection_id,
                'Collection Name': collection_name,
                'Handle': handle,
                'Description': description,
                'Meta Title': meta_title,
                'Meta Description': meta_description,
                'Status': status
            }

    except Exception as e:
        print(f"🔥 Error generating meta data for {collection_name}: {e}")
        traceback.print_exc()
        return {
            'Collection ID': collection.get('id', 'Unknown'),
            'Collection Name': collection.get('name', ''),
            'Handle': collection.get('handle', ''),
            'Description': collection.get('description', ''),
            'Meta Title': f"Error: {str(e)}",
            'Meta Description': f"Error: {str(e)}",
            'Status': 'Error'
        }

def process_collections_in_batches(collections, batch_size=10, max_workers=5):
    """Process collections in batches to avoid context window limitations, with parallel processing"""
    results = []
    total_batches = (len(collections) + batch_size - 1) // batch_size

    print(f"Processing {len(collections)} collections in {total_batches} batches of {batch_size}")
    print(f"Using parallel processing with {max_workers} workers")

    for i in range(0, len(collections), batch_size):
        batch = collections[i:i+batch_size]
        print(f"\n--- Processing batch {i//batch_size + 1}/{total_batches} ---")

        # Process collections in parallel
        batch_results = []
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all tasks
            future_to_collection = {executor.submit(generate_meta_data, collection, use_fast_model=collection.get('use_fast_model', False)): collection for collection in batch}

            # Process results as they complete
            for future in tqdm(as_completed(future_to_collection), total=len(batch), desc="Generating meta data"):
                collection = future_to_collection[future]
                try:
                    result = future.result()
                    if result:
                        batch_results.append(result)
                        # Print the result immediately
                        print(f"\n--- Result for {result['Collection Name']} ---")
                        print(f"Meta Title: {result['Meta Title']}")
                        print(f"Meta Description: {result['Meta Description']}")
                        print(f"Status: {result['Status']}")
                        print("-" * 50)
                except Exception as e:
                    print(f"Error processing collection {collection.get('name', 'Unknown')}: {e}")
                    traceback.print_exc()

        # Add the batch results to the overall results
        results.extend(batch_results)

        # Save results after each batch is complete
        if batch_results:
            print(f"Saving batch {i//batch_size + 1}/{total_batches} results to CSV...")
            # Save just this batch's results to ensure they're saved properly
            save_results_to_csv(batch_results)
            print(f"Batch {i//batch_size + 1}/{total_batches} saved successfully.")
        else:
            print(f"No results in batch {i//batch_size + 1}/{total_batches} to save.")

        # Wait a bit between batches to avoid rate limiting
        if i + batch_size < len(collections):
            print("Waiting 3 seconds before processing next batch...")
            time.sleep(3)

    return results

def get_processed_collection_ids():
    """Read previously processed collection IDs from the CSV file"""
    processed_ids = set()
    output_path = os.path.join(os.path.dirname(__file__), 'collections_meta_results.csv')

    if not os.path.exists(output_path):
        return processed_ids

    try:
        # First, try to count total lines in the file
        total_lines = 0
        with open(output_path, 'r', encoding='utf-8') as f:
            for _ in f:
                total_lines += 1

        # Now read the CSV properly
        with open(output_path, 'r', newline='', encoding='utf-8') as csvfile:
            # Try to read the header first to determine the format
            first_line = csvfile.readline().strip()
            csvfile.seek(0)  # Go back to the beginning of the file

            # Check if 'Collection ID' is in the header
            if 'Collection ID' in first_line:
                reader = csv.DictReader(csvfile)
                for row in reader:
                    if 'Collection ID' in row and row['Collection ID']:
                        processed_ids.add(row['Collection ID'])
            else:
                # Try to read as a regular CSV with the first column as the ID
                reader = csv.reader(csvfile)
                header = next(reader)  # Skip header
                for row in reader:
                    if row and row[0]:  # If row is not empty and first column has a value
                        processed_ids.add(row[0])

        print(f"Found {len(processed_ids)} previously processed collections out of {total_lines-1} total entries")

        # If there's a significant discrepancy, show a warning
        if total_lines > 1 and len(processed_ids) < (total_lines - 1) * 0.9:
            print(f"⚠️ Warning: Only {len(processed_ids)} valid collection IDs found out of {total_lines-1} entries.")
            print("   This may indicate a format issue in the CSV file.")
    except Exception as e:
        print(f"Error reading processed collection IDs: {e}")
        import traceback
        traceback.print_exc()

    return processed_ids

def save_results_to_csv(results):
    """Save results to a CSV file, appending to existing data"""
    if not results:
        print("No results to save.")
        return

    output_path = os.path.join(os.path.dirname(__file__), 'collections_meta_results.csv')
    existing_data = []
    fieldnames = ['Collection ID', 'Collection Name', 'Handle', 'Description', 'Meta Title', 'Meta Description', 'Status', 'Timestamp']

    # Add timestamp to each result
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    for result in results:
        result['Timestamp'] = timestamp

    # Read existing data if file exists
    if os.path.exists(output_path):
        try:
            with open(output_path, 'r', newline='', encoding='utf-8') as csvfile:
                reader = csv.DictReader(csvfile)
                # Get fieldnames from existing file
                existing_fieldnames = reader.fieldnames

                # If 'Timestamp' is not in existing fieldnames, we'll add it
                if existing_fieldnames and 'Timestamp' not in existing_fieldnames:
                    existing_fieldnames.append('Timestamp')
                    fieldnames = existing_fieldnames

                # Read existing data
                for row in reader:
                    # Add timestamp if it doesn't exist
                    if 'Timestamp' not in row:
                        row['Timestamp'] = 'Pre-existing'
                    existing_data.append(row)
        except Exception as e:
            print(f"Error reading existing data: {e}")
            import traceback
            traceback.print_exc()

    # Combine existing data with new results
    # Use collection ID as key to avoid duplicates
    combined_data = {item['Collection ID']: item for item in existing_data}
    for result in results:
        if 'Collection ID' in result and result['Collection ID']:
            combined_data[result['Collection ID']] = result
        else:
            print(f"Warning: Skipping result without Collection ID: {result}")

    # Write combined data back to CSV
    try:
        with open(output_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            for result in combined_data.values():
                writer.writerow(result)

        print(f"✅ Results saved to {output_path} (Total: {len(combined_data)} entries)")

        # Verify the file was written correctly
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path)
            print(f"   File size: {file_size} bytes")
            if file_size == 0:
                print("⚠️ Warning: CSV file is empty after saving!")
    except Exception as e:
        print(f"❌ Error saving results to CSV: {e}")
        import traceback
        traceback.print_exc()

def main():
    # Get previously processed collection IDs first
    processed_ids = get_processed_collection_ids()

    # Ask if user wants to process all collections or select specific ones
    print("\nChoose an option:")
    print("1. Process all collections")
    print("2. Process specific collections by handle")
    print("3. Process only new collections (skip previously processed)")
    print("4. Reprocess specific collections (even if previously processed)")
    print("5. 🕒 Process RECENT collections only (last 24 hours)")
    print("6. 🔧 Debug: Show sample collection data")

    choice = input("Enter your choice (1-6): ").strip()

    collections = []

    if choice == '5':
        # Ask for custom hours or use default 24
        hours_input = input("⏰ Enter hours to look back (default: 24): ").strip()
        try:
            hours = int(hours_input) if hours_input else 24
            if hours <= 0:
                print("❌ Hours must be positive! Using default 24 hours.")
                hours = 24
        except ValueError:
            print("❌ Invalid input! Using default 24 hours.")
            hours = 24

        print(f"🔄 Fetching collections created in the last {hours} hours...")
        collections = fetch_recent_collections_from_shopify(hours)
    elif choice == '6':
        print("\n🔧 Debugging collection data...")
        debug_collection_data()
        return
    else:
        print("🔄 Fetching all collections from Shopify...")
        collections = fetch_collections_from_shopify()

    if not collections:
        print("❌ No collections found. Exiting.")
        return

    print(f"✅ Found {len(collections)} collections")

    collections_to_process = []

    if choice == '1':
        collections_to_process = collections
    elif choice == '2':
        print("Enter collection handles (one per line, empty line to finish):")
        handles = []
        while True:
            handle = input("Collection handle: ").strip()
            if not handle:
                break
            handles.append(handle)

        collections_to_process = [c for c in collections if c['handle'] in handles]
        print(f"Found {len(collections_to_process)} matching collections")
    elif choice == '3':
        # Skip previously processed collections
        collections_to_process = [c for c in collections if str(c['id']) not in processed_ids]
        print(f"Found {len(collections_to_process)} new collections to process")
    elif choice == '4':
        print("Enter collection handles to reprocess (one per line, empty line to finish):")
        handles = []
        while True:
            handle = input("Collection handle: ").strip()
            if not handle:
                break
            handles.append(handle)

        collections_to_process = [c for c in collections if c['handle'] in handles]
        print(f"Found {len(collections_to_process)} matching collections to reprocess")
    elif choice == '5':
        # For recent collections, process all of them (they're already filtered)
        collections_to_process = collections
        print(f"Processing all {len(collections_to_process)} recent collections")

        # Show which collections will be processed
        if collections_to_process:
            print("\n📋 Recent collections to process:")
            for i, collection in enumerate(collections_to_process):
                date_field = collection.get('_date_used', 'unknown')
                creation_date = collection.get('_creation_date')
                if creation_date:
                    date_str = creation_date.strftime('%Y-%m-%d %H:%M:%S UTC')
                    print(f"   {i+1}. {collection['name']} ({date_field}: {date_str})")
                else:
                    print(f"   {i+1}. {collection['name']} (date not available)")

            # Ask for confirmation
            confirm = input(f"\n❓ Process these {len(collections_to_process)} recent collections? (y/n): ").strip().lower()
            if confirm != 'y':
                print("❌ Operation cancelled")
                return
    else:
        print("Invalid choice. Please enter 1, 2, 3, 4, 5, or 6. Exiting.")
        return

    if not collections_to_process:
        print("No collections to process. Exiting.")
        return

    # Ask for performance options
    print("\nPerformance Options:")

    # Ask for model choice
    print("Choose AI model:")
    print("1. GPT-4o (Higher quality, slower)")
    print("2. GPT-3.5-turbo (Faster, good quality)")
    model_choice = input("Enter your choice (1-2): ").strip()
    use_fast_model = model_choice == '2'

    # Ask for parallel processing
    print("\nParallel Processing:")
    print("How many collections would you like to process in parallel? (1-10)")
    print("Higher values = faster processing but may cause rate limiting")
    try:
        max_workers = int(input("Enter number of parallel workers (default: 5): ").strip() or "5")
        max_workers = max(1, min(10, max_workers))  # Limit between 1 and 10
    except ValueError:
        max_workers = 5
        print("Invalid input, using default value of 5 workers")

    # Ask for batch size
    print("\nBatch Size:")
    print("How many collections would you like to process in each batch? (5-50)")
    try:
        batch_size = int(input("Enter batch size (default: 10): ").strip() or "10")
        batch_size = max(5, min(50, batch_size))  # Limit between 5 and 50
    except ValueError:
        batch_size = 10
        print("Invalid input, using default batch size of 10")

    # Process collections in batches with the selected options
    print(f"\nStarting processing with:")
    print(f"- Model: {'GPT-3.5-turbo (Fast)' if use_fast_model else 'GPT-4o (High Quality)'}")
    print(f"- Parallel workers: {max_workers}")
    print(f"- Batch size: {batch_size}")

    # Update the generate_meta_data function to use the fast model option
    for collection in collections_to_process:
        collection['use_fast_model'] = use_fast_model

    results = process_collections_in_batches(collections_to_process, batch_size=batch_size, max_workers=max_workers)

    # Summary
    updated_count = sum(1 for r in results if r['Status'] == 'Updated' or r['Status'] == 'Updated (parsed)')
    failed_count = sum(1 for r in results if 'Failed' in r['Status'] or 'Error' in r['Status'])
    print(f"\n=== SUMMARY ===")
    print(f"Total collections processed: {len(results)}")
    print(f"Updated: {updated_count}")
    print(f"Failed: {failed_count}")

if __name__ == "__main__":
    main()
