import g4f
import json
import os
import traceback
import csv
import re
import requests
import time
import datetime
from datetime import datetime as dt, timedelta, timezone
from tqdm import tqdm
from concurrent.futures import ThreadPoolExecutor, as_completed
from requests.exceptions import SSLError
from urllib3.exceptions import MaxRetryError

# Load the JSON config file
def load_config():
    try:
        with open('../config.json', 'r') as config_file:
            return json.load(config_file)
    except FileNotFoundError:
        print("❌ Config file not found. Please make sure config.json exists.")
        exit(1)

config = load_config()

# Shopify API credentials
password = config['shopify']['password']
store_address = config['shopify']['store_address']
product_name = config['shopify'].get('product_name', "Nuve Radiance – At-Home RF Lifting & Firming")
brand_name = config['shopify'].get('brandname', "Nuve Radiant™")

# API headers
headers = {
    "Content-Type": "application/json",
    "X-Shopify-Access-Token": password
}

def generate_chat_completion(messages, max_retries=5, use_fast_model=False):
    """Generate chat completion with PollinationsAI priority"""
    model = 'gpt-4o-mini' if use_fast_model else 'gpt-4o'
    print(f"Using model: {model}")

    # Try PollinationsAI first
    for attempt in range(max_retries):
        try:
            response = g4f.ChatCompletion.create(
                model=model,
                messages=messages,
                provider=g4f.Provider.PollinationsAI,
                timeout=60
            )
            if response and len(str(response).strip()) > 10:
                return response
        except Exception as e:
            print(f"PollinationsAI attempt {attempt+1}/{max_retries} failed: {str(e)[:50]}...")
            if attempt < max_retries - 1:
                time.sleep(2)

    # Fallback to auto-selection
    for attempt in range(max_retries):
        try:
            response = g4f.ChatCompletion.create(
                model=model,
                messages=messages,
                timeout=60
            )
            if response and len(str(response).strip()) > 10:
                print("✅ Success with auto-selection")
                return response
        except Exception as e:
            print(f"Auto-selection attempt {attempt+1}/{max_retries} failed: {str(e)[:50]}...")
            if attempt < max_retries - 1:
                time.sleep(2)

    print(f"Failed to generate completion after {max_retries} attempts")
    return None

def fetch_blogs_from_shopify(max_retries=5):
    """Fetch blog posts from Shopify API with pagination support"""
    blog_posts = []

    # First, get all blogs
    url = f"{store_address}/api/2023-01/blogs.json"
    blogs = []

    for attempt in range(max_retries):
        try:
            response = requests.get(url, headers=headers, timeout=30)

            if response.status_code == 200:
                blogs = response.json().get('blogs', [])
                break
            else:
                print(f"❌ Failed to fetch blogs: {response.status_code} - {response.text}")
                if attempt < max_retries - 1:
                    time.sleep(2)  # Wait before retrying
        except Exception as e:
            print(f"Error on attempt {attempt+1}/{max_retries} while fetching blogs: {e}")
            if attempt < max_retries - 1:
                time.sleep(2)  # Wait before retrying

    if not blogs:
        print("⚠️ No blogs found or couldn't fetch blogs")
        return blog_posts

    # Now fetch articles for each blog with pagination
    for blog in blogs:
        blog_id = blog['id']
        print(f"Fetching articles for blog ID: {blog_id}")

        # Start with the first page
        page_url = f"{store_address}/api/2023-01/blogs/{blog_id}/articles.json?limit=250"

        while page_url:  # Continue until we have no more pages
            next_page_url = None

            for attempt in range(max_retries):
                try:
                    print(f"Fetching: {page_url}")
                    response = requests.get(page_url, headers=headers, timeout=30)

                    if response.status_code == 200:
                        articles = response.json().get('articles', [])

                        print(f"Found {len(articles)} articles on this page")
                        for article in articles:
                            blog_posts.append({
                                'id': article['id'],
                                'blog_id': blog_id,
                                'title': article['title'],
                                'handle': article['handle'],
                                'content': article.get('body_html', ''),
                                'summary': article.get('summary_html', '')
                            })

                        # Check for pagination in the Link header
                        link_header = response.headers.get('Link', '')
                        if 'rel="next"' in link_header:
                            # Extract the URL for the next page
                            for part in link_header.split(','):
                                if 'rel="next"' in part:
                                    next_page_url = part.split(';')[0].strip('<> ')
                                    break

                        break  # Break out of retry loop on success
                    else:
                        print(f"❌ Failed to fetch articles for blog {blog_id}: {response.status_code} - {response.text}")
                        if attempt < max_retries - 1:
                            time.sleep(2)  # Wait before retrying
                except Exception as e:
                    print(f"Error on attempt {attempt+1}/{max_retries} while fetching articles: {e}")
                    if attempt < max_retries - 1:
                        time.sleep(2)  # Wait before retrying

            # Move to the next page or exit the loop if no more pages
            page_url = next_page_url

            # Add a small delay between pagination requests to avoid rate limiting
            if page_url:
                time.sleep(1)

    print(f"Total blog posts fetched: {len(blog_posts)}")
    return blog_posts

def fetch_recent_blogs_from_shopify(hours=24, max_retries=5):
    """Fetch blog posts created in the last specified hours from Shopify API"""
    print(f"🔄 Fetching blog posts created in the last {hours} hours...")

    # Calculate the datetime cutoff
    cutoff_time = dt.now(timezone.utc) - timedelta(hours=hours)
    cutoff_time_str = cutoff_time.strftime('%Y-%m-%dT%H:%M:%S+00:00')
    print(f"🕒 Looking for blog posts created after: {cutoff_time.strftime('%Y-%m-%d %H:%M:%S UTC')}")

    blog_posts = []

    # First, get all blogs
    url = f"{store_address}/api/2023-01/blogs.json"
    blogs = []

    for attempt in range(max_retries):
        try:
            response = requests.get(url, headers=headers, timeout=30)

            if response.status_code == 200:
                blogs = response.json().get('blogs', [])
                break
            else:
                print(f"❌ Failed to fetch blogs: {response.status_code} - {response.text}")
                if attempt < max_retries - 1:
                    time.sleep(2)  # Wait before retrying
        except Exception as e:
            print(f"Error on attempt {attempt+1}/{max_retries} while fetching blogs: {e}")
            if attempt < max_retries - 1:
                time.sleep(2)  # Wait before retrying

    if not blogs:
        print("⚠️ No blogs found or couldn't fetch blogs")
        return blog_posts

    # Now fetch articles for each blog with date filtering
    for blog in blogs:
        blog_id = blog['id']
        print(f"Fetching recent articles for blog ID: {blog_id}")

        # Start with the first page, using created_at_min filter
        page_url = f"{store_address}/api/2023-01/blogs/{blog_id}/articles.json?limit=250&created_at_min={cutoff_time_str}"

        while page_url:  # Continue until we have no more pages
            next_page_url = None

            for attempt in range(max_retries):
                try:
                    print(f"Fetching: {page_url}")
                    response = requests.get(page_url, headers=headers, timeout=30)

                    if response.status_code == 200:
                        articles = response.json().get('articles', [])

                        print(f"Found {len(articles)} articles on this page")

                        # Additional client-side filtering to ensure we only get recent articles
                        for article in articles:
                            # Try created_at first, then published_at as fallback
                            date_field = None
                            date_value = None

                            if 'created_at' in article and article['created_at']:
                                date_field = 'created_at'
                                date_value = article['created_at']
                            elif 'published_at' in article and article['published_at']:
                                date_field = 'published_at'
                                date_value = article['published_at']

                            if date_value:
                                try:
                                    # Parse the timestamp
                                    date_str = date_value
                                    # Handle different timestamp formats
                                    if date_str.endswith('Z'):
                                        date_str = date_str[:-1] + '+00:00'
                                    elif '+' not in date_str and 'T' in date_str:
                                        date_str += '+00:00'

                                    parsed_date = dt.fromisoformat(date_str)

                                    # Make sure both datetimes are timezone-aware for comparison
                                    if parsed_date.tzinfo is None:
                                        parsed_date = parsed_date.replace(tzinfo=timezone.utc)

                                    if parsed_date >= cutoff_time:
                                        blog_post_data = {
                                            'id': article['id'],
                                            'blog_id': blog_id,
                                            'title': article['title'],
                                            'handle': article['handle'],
                                            'content': article.get('body_html', ''),
                                            'summary': article.get('summary_html', ''),
                                            '_date_used': date_field,
                                            '_creation_date': parsed_date
                                        }
                                        blog_posts.append(blog_post_data)
                                        print(f"   ✅ Recent: {article['title']} ({date_field}: {parsed_date.strftime('%Y-%m-%d %H:%M:%S UTC')})")

                                except (ValueError, TypeError) as e:
                                    print(f"⚠️  Could not parse {date_field} for article '{article.get('title', 'Unknown')}': {e}")
                                    continue
                            else:
                                # If no date available, include it (fallback)
                                blog_post_data = {
                                    'id': article['id'],
                                    'blog_id': blog_id,
                                    'title': article['title'],
                                    'handle': article['handle'],
                                    'content': article.get('body_html', ''),
                                    'summary': article.get('summary_html', ''),
                                    '_date_used': 'none',
                                    '_creation_date': None
                                }
                                blog_posts.append(blog_post_data)

                        # Check for pagination in the Link header
                        link_header = response.headers.get('Link', '')
                        if 'rel="next"' in link_header:
                            # Extract the URL for the next page
                            for part in link_header.split(','):
                                if 'rel="next"' in part:
                                    next_page_url = part.split(';')[0].strip('<> ')
                                    break

                        break  # Break out of retry loop on success
                    else:
                        print(f"❌ Failed to fetch articles for blog {blog_id}: {response.status_code} - {response.text}")
                        if attempt < max_retries - 1:
                            time.sleep(2)  # Wait before retrying
                except Exception as e:
                    print(f"Error on attempt {attempt+1}/{max_retries} while fetching articles: {e}")
                    if attempt < max_retries - 1:
                        time.sleep(2)  # Wait before retrying

            # Move to the next page or exit the loop if no more pages
            page_url = next_page_url

            # Add a small delay between pagination requests to avoid rate limiting
            if page_url:
                time.sleep(1)

    print(f"🆕 Recent blog posts found: {len(blog_posts)}")
    return blog_posts

def debug_blog_data(max_retries=5):
    """Debug function to show sample blog post data structure"""
    print("🔍 Fetching sample blog posts to debug data structure...")

    # First, get blogs
    url = f"{store_address}/api/2023-01/blogs.json"
    blogs = []

    for attempt in range(max_retries):
        try:
            response = requests.get(url, headers=headers, timeout=30)
            if response.status_code == 200:
                blogs = response.json().get('blogs', [])
                break
            else:
                print(f"❌ Failed to fetch blogs: {response.status_code}")
                if attempt < max_retries - 1:
                    time.sleep(2)
        except Exception as e:
            print(f"Error fetching blogs (attempt {attempt+1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                time.sleep(2)

    if not blogs:
        print("❌ No blogs found")
        return

    # Get a few articles from the first blog
    blog_id = blogs[0]['id']
    print(f"📊 Fetching sample articles from blog ID: {blog_id}")

    url = f"{store_address}/api/2023-01/blogs/{blog_id}/articles.json?limit=3"

    for attempt in range(max_retries):
        try:
            response = requests.get(url, headers=headers, timeout=30)
            if response.status_code == 200:
                articles = response.json().get('articles', [])

                print(f"📊 Sample of {len(articles)} blog posts:")
                for i, article in enumerate(articles):
                    print(f"\n🔸 Blog Post {i+1}:")
                    print(f"   ID: {article.get('id', 'N/A')}")
                    print(f"   Title: {article.get('title', 'N/A')}")
                    print(f"   Handle: {article.get('handle', 'N/A')}")
                    print(f"   Created At: {article.get('created_at', 'N/A')}")
                    print(f"   Published At: {article.get('published_at', 'N/A')}")
                    print(f"   Updated At: {article.get('updated_at', 'N/A')}")
                    print(f"   Available fields: {list(article.keys())}")

                return articles
            else:
                print(f"❌ Failed to fetch articles: {response.status_code}")
                if attempt < max_retries - 1:
                    time.sleep(2)
        except Exception as e:
            print(f"Error fetching articles (attempt {attempt+1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                time.sleep(2)

    print("❌ Failed to fetch sample articles")
    return []

def update_blog_seo(article_id, blog_id, meta_title, meta_description, max_retries=5):
    """Update blog post SEO meta fields"""
    url = f"{store_address}/api/2023-01/blogs/{blog_id}/articles/{article_id}.json"

    payload = {
        "article": {
            "id": article_id,
            "metafields": [
                {
                    "namespace": "global",
                    "key": "title_tag",
                    "value": meta_title,
                    "type": "single_line_text_field"
                },
                {
                    "namespace": "global",
                    "key": "description_tag",
                    "value": meta_description,
                    "type": "multi_line_text_field"
                }
            ]
        }
    }

    for attempt in range(max_retries):
        try:
            response = requests.put(url, headers=headers, json=payload)

            if response.status_code == 200:
                print(f"✅ Updated SEO for article {article_id}")
                return True
            else:
                print(f"❌ Failed to update SEO for article {article_id}: {response.status_code} - {response.text}")
                if attempt < max_retries - 1:
                    time.sleep(2)  # Wait before retrying
        except (SSLError, MaxRetryError) as e:
            print(f"SSL error on attempt {attempt+1}/{max_retries} while updating article: {e}")
            if attempt < max_retries - 1:
                time.sleep(2)  # Wait before retrying

    print(f"Failed to update SEO for article {article_id} after {max_retries} attempts")
    return False

def generate_meta_data(blog_post, max_retries=5, use_fast_model=False):
    """Generate SEO meta title and description for a blog post"""
    try:
        title = blog_post['title']
        content = blog_post['content']
        summary = blog_post['summary']
        article_id = blog_post['id']
        blog_id = blog_post['blog_id']
        handle = blog_post['handle']

        # Use summary if available, otherwise use a truncated version of the content
        description_text = summary if summary else content[:500] + "..." if len(content) > 500 else content

        print(f"🧠 Generating SEO meta data for blog: {title}")

        messages = [
            {"role": "system", "content": f'''
                You are an SEO expert specializing in e-commerce blog meta titles and descriptions.
                Your task is to create highly optimized SEO meta titles and descriptions for Shopify blog posts.

                IMPORTANT: You are creating the SEO meta title and meta description that appear in the "Search engine listing" section of Shopify.
                These are NOT replacing the actual blog title or content.

                The product being sold is: {product_name}
                The brand name is: {brand_name}

                Guidelines:
                - Meta titles should be 50-60 characters for optimal display in search results
                - Meta descriptions should be 150-160 characters for optimal display
                - Include relevant keywords that will drive sales
                - Create compelling, action-oriented content that encourages clicks
                - Focus on benefits and unique selling points
                - Include a clear call to action in the description
                - Maintain the core message from the original blog title and content
                - Optimize for high conversion, targeting customers ready to purchase
                - Focus on customers who will make fast purchasing decisions

                Format your response as a JSON object with two fields:
                - meta_title: The optimized SEO meta title (for the "Page title" field in Shopify's Search engine listing section)
                - meta_description: The optimized SEO meta description (for the "Meta description" field in Shopify's Search engine listing section)
            '''},
            {"role": "user", "content": f'''
                Please create an optimized SEO meta title and meta description for this blog post:

                Blog Title: {title}
                Blog Content/Summary: {description_text}

                These are for the SEO fields in the "Search engine listing" section, NOT to replace the actual blog title or content.

                Return only the JSON object with meta_title and meta_description fields.
            '''}
        ]

        meta_data = generate_chat_completion(messages, max_retries, use_fast_model)

        if not meta_data:
            print(f"❌ Failed to generate meta data for {title}")
            return {
                'Article ID': article_id,
                'Blog ID': blog_id,
                'Title': title,
                'Handle': handle,
                'Meta Title': '',
                'Meta Description': '',
                'Status': 'Failed - No response from AI'
            }

        # Parse the JSON response
        try:
            # Clean up the response to ensure it's valid JSON
            cleaned_response = re.search(r'\{.*\}', meta_data, re.DOTALL)
            if cleaned_response:
                meta_data = cleaned_response.group(0)

            meta_data_json = json.loads(meta_data)
            meta_title = meta_data_json.get('meta_title', '')
            meta_description = meta_data_json.get('meta_description', '')

            # Update the blog post in Shopify
            update_success = update_blog_seo(article_id, blog_id, meta_title, meta_description)

            status = "Updated" if update_success else "Failed to update"

            return {
                'Article ID': article_id,
                'Blog ID': blog_id,
                'Title': title,
                'Handle': handle,
                'Meta Title': meta_title,
                'Meta Description': meta_description,
                'Status': status
            }
        except json.JSONDecodeError:
            # If the response isn't valid JSON, try to extract the data using string parsing
            print(f"⚠️ JSON parsing failed for {title}, attempting string extraction")

            meta_title = ""
            meta_description = ""

            if "meta_title" in meta_data:
                meta_title_start = meta_data.find("meta_title") + len("meta_title") + 2
                meta_title_end = meta_data.find('"', meta_title_start + 1)
                if meta_title_end > meta_title_start:
                    meta_title = meta_data[meta_title_start:meta_title_end].strip()

            if "meta_description" in meta_data:
                meta_desc_start = meta_data.find("meta_description") + len("meta_description") + 2
                meta_desc_end = meta_data.find('"', meta_desc_start + 1)
                if meta_desc_end > meta_desc_start:
                    meta_description = meta_data[meta_desc_start:meta_desc_end].strip()

            # Update the blog post in Shopify
            update_success = update_blog_seo(article_id, blog_id, meta_title, meta_description)

            status = "Updated (parsed)" if update_success else "Failed to update"

            return {
                'Article ID': article_id,
                'Blog ID': blog_id,
                'Title': title,
                'Handle': handle,
                'Meta Title': meta_title,
                'Meta Description': meta_description,
                'Status': status
            }

    except Exception as e:
        print(f"🔥 Error generating meta data for {blog_post.get('title', 'Unknown')}: {e}")
        traceback.print_exc()
        return {
            'Article ID': blog_post.get('id', 'Unknown'),
            'Blog ID': blog_post.get('blog_id', 'Unknown'),
            'Title': blog_post.get('title', 'Unknown'),
            'Handle': blog_post.get('handle', ''),
            'Meta Title': f"Error: {str(e)}",
            'Meta Description': f"Error: {str(e)}",
            'Status': 'Error'
        }

def process_blogs_in_batches(blog_posts, batch_size=200, max_workers=5):
    """Process blog posts in batches to avoid context window limitations, with parallel processing"""
    results = []
    total_batches = (len(blog_posts) + batch_size - 1) // batch_size

    print(f"Processing {len(blog_posts)} blog posts in {total_batches} batches of {batch_size}")
    print(f"Using parallel processing with {max_workers} workers")

    for i in range(0, len(blog_posts), batch_size):
        batch = blog_posts[i:i+batch_size]
        print(f"\n--- Processing batch {i//batch_size + 1}/{total_batches} ---")

        # Process blog posts in parallel
        batch_results = []
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all tasks
            future_to_blog = {executor.submit(generate_meta_data, blog_post, use_fast_model=blog_post.get('use_fast_model', False)): blog_post for blog_post in batch}

            # Process results as they complete
            for future in tqdm(as_completed(future_to_blog), total=len(batch), desc="Generating meta data"):
                blog_post = future_to_blog[future]
                try:
                    result = future.result()
                    if result:
                        batch_results.append(result)
                        # Print the result immediately
                        print(f"\n--- Result for {result['Title']} ---")
                        print(f"Meta Title: {result['Meta Title']}")
                        print(f"Meta Description: {result['Meta Description']}")
                        print(f"Status: {result['Status']}")
                        print("-" * 50)

                        # Save each result immediately to CSV
                        save_results_to_csv([result])
                except Exception as e:
                    print(f"Error processing blog post {blog_post.get('title', 'Unknown')}: {e}")
                    traceback.print_exc()

        results.extend(batch_results)

        # Save intermediate results after each batch (redundant but keeping for safety)
        save_results_to_csv(results)

        # Wait a bit between batches to avoid rate limiting
        if i + batch_size < len(blog_posts):
            print("Waiting 3 seconds before processing next batch...")
            time.sleep(3)

    return results

def get_processed_article_ids():
    """Read previously processed article IDs from the CSV file"""
    processed_ids = set()
    output_path = os.path.join(os.path.dirname(__file__), 'blog_meta_results.csv')

    if not os.path.exists(output_path):
        return processed_ids

    try:
        # First, count total lines to verify file integrity
        total_lines = 0
        with open(output_path, 'r', encoding='utf-8') as f:
            for _ in f:
                total_lines += 1

        # Now read the CSV properly
        with open(output_path, 'r', newline='', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            for row in reader:
                # Check if Article ID exists and is not empty
                if 'Article ID' in row and row['Article ID']:
                    processed_ids.add(row['Article ID'])

        print(f"Found {len(processed_ids)} previously processed articles out of {total_lines-1} total entries")

        # If there's a significant discrepancy, show a warning
        if total_lines > 1 and len(processed_ids) < (total_lines - 1) * 0.9:
            print(f"⚠️ Warning: Found {len(processed_ids)} valid article IDs but the CSV has {total_lines-1} entries.")
            print("   This may indicate corrupted data in the CSV file.")

    except Exception as e:
        print(f"Error reading processed article IDs: {e}")
        traceback.print_exc()

    return processed_ids

def save_results_to_csv(results):
    """Save results to a CSV file, appending to existing data"""
    output_path = os.path.join(os.path.dirname(__file__), 'blog_meta_results.csv')
    existing_data = []
    fieldnames = ['Article ID', 'Blog ID', 'Title', 'Handle', 'Meta Title', 'Meta Description', 'Status', 'Timestamp']

    # Add timestamp to each result
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    for result in results:
        result['Timestamp'] = timestamp

    # Create a backup of the existing file if it exists
    if os.path.exists(output_path):
        backup_path = f"{output_path}.backup.{timestamp.replace(':', '-').replace(' ', '_')}"
        try:
            import shutil
            shutil.copy2(output_path, backup_path)
            print(f"✅ Created backup of existing CSV file: {backup_path}")
        except Exception as e:
            print(f"⚠️ Warning: Could not create backup: {e}")

    # Read existing data if file exists
    if os.path.exists(output_path):
        try:
            with open(output_path, 'r', newline='', encoding='utf-8') as csvfile:
                reader = csv.DictReader(csvfile)
                # Get fieldnames from existing file
                existing_fieldnames = reader.fieldnames

                # If 'Timestamp' is not in existing fieldnames, we'll add it
                if existing_fieldnames and 'Timestamp' not in existing_fieldnames:
                    existing_fieldnames.append('Timestamp')
                    fieldnames = existing_fieldnames

                # Read existing data
                valid_rows = 0
                invalid_rows = 0
                for row in reader:
                    # Validate that the row has an Article ID
                    if 'Article ID' in row and row['Article ID']:
                        # Add timestamp if it doesn't exist
                        if 'Timestamp' not in row:
                            row['Timestamp'] = 'Pre-existing'
                        existing_data.append(row)
                        valid_rows += 1
                    else:
                        invalid_rows += 1

                if invalid_rows > 0:
                    print(f"⚠️ Warning: Found {invalid_rows} rows with missing or invalid Article IDs")

        except Exception as e:
            print(f"Error reading existing data: {e}")
            traceback.print_exc()

    # Combine existing data with new results
    # Use article ID as key to avoid duplicates
    combined_data = {}
    for item in existing_data:
        if 'Article ID' in item and item['Article ID']:
            combined_data[item['Article ID']] = item

    for result in results:
        if 'Article ID' in result and result['Article ID']:
            combined_data[result['Article ID']] = result

    # Write combined data back to CSV
    try:
        with open(output_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            for result in combined_data.values():
                writer.writerow(result)

        print(f"✅ Results saved to {output_path} (Total: {len(combined_data)} entries)")
    except Exception as e:
        print(f"❌ Error saving results to CSV: {e}")
        traceback.print_exc()

def main():
    # Get previously processed article IDs first
    processed_ids = get_processed_article_ids()

    # Ask if user wants to process all blog posts or select specific ones
    print("\nChoose an option:")
    print("1. Process all blog posts")
    print("2. Process specific blog posts by handle")
    print("3. Process only new blog posts (skip previously processed)")
    print("4. Reprocess specific blog posts (even if previously processed)")
    print("5. 🕒 Process RECENT blog posts only (last 24 hours)")
    print("6. 🔧 Debug: Show sample blog post data")

    choice = input("Enter your choice (1-6): ").strip()

    blog_posts = []

    if choice == '5':
        # Ask for custom hours or use default 24
        hours_input = input("⏰ Enter hours to look back (default: 24): ").strip()
        try:
            hours = int(hours_input) if hours_input else 24
            if hours <= 0:
                print("❌ Hours must be positive! Using default 24 hours.")
                hours = 24
        except ValueError:
            print("❌ Invalid input! Using default 24 hours.")
            hours = 24

        print(f"🔄 Fetching blog posts created in the last {hours} hours...")
        blog_posts = fetch_recent_blogs_from_shopify(hours)
    elif choice == '6':
        print("\n🔧 Debugging blog post data...")
        debug_blog_data()
        return
    else:
        print("🔄 Fetching all blog posts from Shopify...")
        blog_posts = fetch_blogs_from_shopify()

    if not blog_posts:
        print("❌ No blog posts found. Exiting.")
        return

    print(f"✅ Found {len(blog_posts)} blog posts")

    blog_posts_to_process = []

    if choice == '1':
        blog_posts_to_process = blog_posts
    elif choice == '2':
        print("Enter blog post handles (one per line, empty line to finish):")
        handles = []
        while True:
            handle = input("Blog post handle: ").strip()
            if not handle:
                break
            handles.append(handle)

        blog_posts_to_process = [p for p in blog_posts if p['handle'] in handles]
        print(f"Found {len(blog_posts_to_process)} matching blog posts")
    elif choice == '3':
        # Skip previously processed articles
        blog_posts_to_process = [p for p in blog_posts if str(p['id']) not in processed_ids]
        print(f"Found {len(blog_posts_to_process)} new blog posts to process")
    elif choice == '4':
        print("Enter blog post handles to reprocess (one per line, empty line to finish):")
        handles = []
        while True:
            handle = input("Blog post handle: ").strip()
            if not handle:
                break
            handles.append(handle)

        blog_posts_to_process = [p for p in blog_posts if p['handle'] in handles]
        print(f"Found {len(blog_posts_to_process)} matching blog posts to reprocess")
    elif choice == '5':
        # For recent blog posts, process all of them (they're already filtered)
        blog_posts_to_process = blog_posts
        print(f"Processing all {len(blog_posts_to_process)} recent blog posts")

        # Show which blog posts will be processed
        if blog_posts_to_process:
            print("\n📋 Recent blog posts to process:")
            for i, post in enumerate(blog_posts_to_process):
                date_field = post.get('_date_used', 'unknown')
                creation_date = post.get('_creation_date')
                if creation_date:
                    date_str = creation_date.strftime('%Y-%m-%d %H:%M:%S UTC')
                    print(f"   {i+1}. {post['title']} ({date_field}: {date_str})")
                else:
                    print(f"   {i+1}. {post['title']} (date not available)")

            # Ask for confirmation
            confirm = input(f"\n❓ Process these {len(blog_posts_to_process)} recent blog posts? (y/n): ").strip().lower()
            if confirm != 'y':
                print("❌ Operation cancelled")
                return
    else:
        print("Invalid choice. Please enter 1, 2, 3, 4, 5, or 6. Exiting.")
        return

    if not blog_posts_to_process:
        print("No blog posts to process. Exiting.")
        return

    # Ask for performance options
    print("\nPerformance Options:")

    # Ask for model choice
    print("Choose AI model:")
    print("1. GPT-4o (Higher quality, slower)")
    print("2. GPT-3.5-turbo (Faster, good quality)")
    model_choice = input("Enter your choice (1-2): ").strip()
    use_fast_model = model_choice == '2'

    # Ask for parallel processing
    print("\nParallel Processing:")
    print("How many blog posts would you like to process in parallel? (1-10)")
    print("Higher values = faster processing but may cause rate limiting")
    try:
        max_workers = int(input("Enter number of parallel workers (default: 5): ").strip() or "5")
        max_workers = max(1, min(10, max_workers))  # Limit between 1 and 10
    except ValueError:
        max_workers = 5
        print("Invalid input, using default value of 5 workers")

    # Ask for batch size
    print("\nBatch Size:")
    print("How many blog posts would you like to process in each batch? (10-500)")
    try:
        batch_size = int(input("Enter batch size (default: 200): ").strip() or "200")
        batch_size = max(10, min(500, batch_size))  # Limit between 10 and 500
    except ValueError:
        batch_size = 200
        print("Invalid input, using default batch size of 200")

    # Process blog posts in batches with the selected options
    print(f"\nStarting processing with:")
    print(f"- Model: {'GPT-3.5-turbo (Fast)' if use_fast_model else 'GPT-4o (High Quality)'}")
    print(f"- Parallel workers: {max_workers}")
    print(f"- Batch size: {batch_size}")

    # Update the generate_meta_data function to use the fast model option
    for blog_post in blog_posts_to_process:
        blog_post['use_fast_model'] = use_fast_model

    results = process_blogs_in_batches(blog_posts_to_process, batch_size=batch_size, max_workers=max_workers)

    # Summary
    updated_count = sum(1 for r in results if r['Status'] == 'Updated' or r['Status'] == 'Updated (parsed)')
    failed_count = sum(1 for r in results if 'Failed' in r['Status'] or 'Error' in r['Status'])

    print(f"\n=== SUMMARY ===")
    print(f"Total blog posts processed: {len(results)}")
    print(f"Updated: {updated_count}")
    print(f"Failed: {failed_count}")

def repair_csv_file():
    """Utility function to repair the CSV file by removing invalid entries"""
    output_path = os.path.join(os.path.dirname(__file__), 'blog_meta_results.csv')

    if not os.path.exists(output_path):
        print(f"❌ CSV file not found: {output_path}")
        return

    # Create a backup
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    backup_path = f"{output_path}.repair_backup.{timestamp.replace(':', '-').replace(' ', '_')}"
    try:
        import shutil
        shutil.copy2(output_path, backup_path)
        print(f"✅ Created backup before repair: {backup_path}")
    except Exception as e:
        print(f"⚠️ Warning: Could not create backup: {e}")
        return

    # Read and validate the CSV
    valid_entries = []
    invalid_entries = 0
    fieldnames = ['Article ID', 'Blog ID', 'Title', 'Handle', 'Meta Title', 'Meta Description', 'Status', 'Timestamp']

    try:
        with open(output_path, 'r', newline='', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            # Get fieldnames from existing file
            existing_fieldnames = reader.fieldnames or fieldnames

            # Read and validate each row
            for row in reader:
                if 'Article ID' in row and row['Article ID']:
                    # Ensure all required fields exist
                    for field in fieldnames:
                        if field not in row:
                            row[field] = ''
                    valid_entries.append(row)
                else:
                    invalid_entries += 1
    except Exception as e:
        print(f"❌ Error reading CSV file: {e}")
        traceback.print_exc()
        return

    # Write back only valid entries
    try:
        with open(output_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            for entry in valid_entries:
                writer.writerow(entry)

        print(f"✅ CSV file repaired successfully")
        print(f"   - Valid entries: {len(valid_entries)}")
        print(f"   - Invalid entries removed: {invalid_entries}")
    except Exception as e:
        print(f"❌ Error writing repaired CSV file: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    # Check for repair command
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "--repair":
        print("Running CSV repair utility...")
        repair_csv_file()
    else:
        main()
