import csv
from dotenv import load_dotenv
import json

# Load the JSON config file
def load_config():
    with open('../config.json', 'r') as config_file:
        return json.load(config_file)

config = load_config()

# Shopify API credentials
input_file = 'inputs/input10.csv'
output_file = "inputs/input10.csv"

with open(input_file, 'r', encoding='utf-8') as infile, \
     open(output_file, 'w', encoding='utf-8', newline='') as outfile:

    reader = csv.reader(infile)
    writer = csv.writer(outfile, quoting=csv.QUOTE_ALL)

    for row in reader:
        # Skip empty or malformed rows
        if len(row) != 3:
            print(f"Skipping malformed row: {row}")
            continue
        # Write row with proper quoting
        writer.writerow(row)

print(f"Fixed CSV saved as {output_file}")
