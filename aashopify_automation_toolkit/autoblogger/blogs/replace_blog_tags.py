#!/usr/bin/env python3
"""
Blog Tags Replacement Script
Fetches blogs (all or recent) and replaces their tags with correct ones from tags.csv
Perfect for fixing tag mismatches like RF skin device blogs having foot pain tags
"""

import json
import os
import csv
import requests
import time
import random
from datetime import datetime, timedelta, timezone
from tqdm import tqdm

# Load the JSON config file
def load_config():
    try:
        with open('../config.json', 'r') as config_file:
            return json.load(config_file)
    except FileNotFoundError:
        print("❌ Config file not found. Please make sure config.json exists.")
        exit(1)

config = load_config()

# Shopify API credentials
api_key = config['shopify']['api_key']
password = config['shopify']['password']
store_address = config['shopify']['store_address']
blog_id = config['shopify']['blog_id']

# API headers
headers = {
    "Content-Type": "application/json",
    "Accept": "application/json"
}

def load_correct_tags():
    """Load the correct tags from tags.csv file"""
    tags_file = 'tags.csv'
    try:
        with open(tags_file, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            tags = []
            for row in reader:
                if 'Tags' in row and row['Tags'].strip():
                    tags.append(row['Tags'].strip())
        
        print(f"✅ Loaded {len(tags)} correct tags from {tags_file}")
        print(f"📋 Sample tags: {', '.join(tags[:5])}...")
        return tags
    except FileNotFoundError:
        print(f"❌ Tags file not found: {tags_file}")
        print("Please make sure tags.csv exists in the blogs directory")
        return []
    except Exception as e:
        print(f"❌ Error loading tags: {e}")
        return []

def fetch_all_blogs(max_retries=5):
    """Fetch all blog posts from Shopify API with pagination support"""
    blog_posts = []
    page_info = None

    print("🔄 Fetching all blog posts from Shopify...")

    while True:
        url = f"{store_address}/blogs/{blog_id}/articles.json?limit=250"
        if page_info:
            url += f"&page_info={page_info}"

        for attempt in range(max_retries):
            try:
                response = requests.get(url, headers=headers, auth=(api_key, password), timeout=30)

                if response.status_code == 200:
                    data = response.json()
                    articles = data.get('articles', [])
                    blog_posts.extend(articles)

                    print(f"📥 Fetched {len(articles)} articles (Total: {len(blog_posts)})")

                    # Check for pagination
                    link_header = response.headers.get('Link', '')
                    if 'rel="next"' in link_header:
                        # Extract page_info from the Link header
                        next_link = [link for link in link_header.split(',') if 'rel="next"' in link][0]
                        page_info = next_link.split('page_info=')[1].split('&')[0].split('>')[0]
                    else:
                        # No more pages
                        break

                    break  # Break out of retry loop on success
                else:
                    print(f"❌ Failed to fetch articles: {response.status_code} - {response.text}")
                    if attempt < max_retries - 1:
                        time.sleep(2)  # Wait before retrying
            except Exception as e:
                print(f"Error on attempt {attempt+1}/{max_retries}: {e}")
                if attempt < max_retries - 1:
                    time.sleep(2)  # Wait before retrying
        else:
            print(f"❌ Failed to fetch articles after {max_retries} attempts")
            break

        # Small delay between pages to be API-friendly
        time.sleep(1)

    print(f"📊 Total blog posts fetched: {len(blog_posts)}")
    return blog_posts

def fetch_blogs_with_time_limit(cutoff_time, max_retries=5):
    """Fetch blogs but stop when we reach articles older than cutoff_time"""
    blog_posts = []
    page_info = None

    print("🔄 Fetching blog posts with time filtering...")

    while True:
        url = f"{store_address}/blogs/{blog_id}/articles.json?limit=250"
        if page_info:
            url += f"&page_info={page_info}"

        for attempt in range(max_retries):
            try:
                response = requests.get(url, headers=headers, auth=(api_key, password), timeout=30)

                if response.status_code == 200:
                    data = response.json()
                    articles = data.get('articles', [])

                    # Check if we should stop fetching (articles are too old)
                    should_continue = False
                    articles_in_range = 0

                    for article in articles:
                        date_value = article.get('created_at') or article.get('published_at')

                        if date_value:
                            try:
                                # Parse the timestamp
                                date_str = date_value
                                if date_str.endswith('Z'):
                                    date_str = date_str[:-1] + '+00:00'
                                elif '+' not in date_str and 'T' in date_str:
                                    date_str += '+00:00'

                                parsed_date = datetime.fromisoformat(date_str)
                                if parsed_date.tzinfo is None:
                                    parsed_date = parsed_date.replace(tzinfo=timezone.utc)

                                if parsed_date >= cutoff_time:
                                    articles_in_range += 1
                                    should_continue = True

                            except (ValueError, TypeError):
                                continue

                    blog_posts.extend(articles)
                    print(f"📥 Fetched {len(articles)} articles ({articles_in_range} in time range, Total: {len(blog_posts)})")

                    # If no articles in this batch are within our time range, stop fetching
                    if not should_continue:
                        print(f"⏹️  Reached articles older than cutoff time. Stopping fetch.")
                        break

                    # Check for pagination
                    link_header = response.headers.get('Link', '')
                    if 'rel="next"' in link_header:
                        # Extract page_info from the Link header
                        next_link = [link for link in link_header.split(',') if 'rel="next"' in link][0]
                        page_info = next_link.split('page_info=')[1].split('&')[0].split('>')[0]
                    else:
                        # No more pages
                        break

                    break  # Break out of retry loop on success
                else:
                    print(f"❌ Failed to fetch articles: {response.status_code} - {response.text}")
                    if attempt < max_retries - 1:
                        time.sleep(2)  # Wait before retrying
            except Exception as e:
                print(f"Error on attempt {attempt+1}/{max_retries}: {e}")
                if attempt < max_retries - 1:
                    time.sleep(2)  # Wait before retrying
        else:
            print(f"❌ Failed to fetch articles after {max_retries} attempts")
            break

        # Small delay between pages to be API-friendly
        time.sleep(1)

    print(f"📊 Total blog posts fetched: {len(blog_posts)}")
    return blog_posts

def fetch_recent_blogs(hours=24, max_retries=5):
    """Fetch blog posts created in the last specified hours - SIMPLE VERSION"""
    print(f"🔄 Fetching blog posts created in the last {hours} hours...")

    # Calculate the datetime cutoff
    cutoff_time = datetime.now(timezone.utc) - timedelta(hours=hours)
    print(f"🕒 Looking for blog posts created after: {cutoff_time.strftime('%Y-%m-%d %H:%M:%S UTC')}")

    blog_posts = []
    page_info = None

    while True:
        url = f"{store_address}/blogs/{blog_id}/articles.json?limit=250"
        if page_info:
            url += f"&page_info={page_info}"

        for attempt in range(max_retries):
            try:
                response = requests.get(url, headers=headers, auth=(api_key, password), timeout=30)

                if response.status_code == 200:
                    data = response.json()
                    articles = data.get('articles', [])

                    # Check each article's date and stop if we've gone too far back
                    recent_found_in_batch = 0

                    for article in articles:
                        # Use published_at as primary date field (as seen in API response)
                        date_value = article.get('published_at') or article.get('created_at')

                        if date_value:
                            try:
                                # Parse the timestamp (handles +02:00 format from API)
                                parsed_date = datetime.fromisoformat(date_value)

                                # Convert to UTC for comparison
                                if parsed_date.tzinfo is None:
                                    parsed_date = parsed_date.replace(tzinfo=timezone.utc)
                                else:
                                    parsed_date = parsed_date.astimezone(timezone.utc)

                                if parsed_date >= cutoff_time:
                                    blog_posts.append(article)
                                    recent_found_in_batch += 1
                                    print(f"   ✅ Recent: {article['title'][:60]}... ({parsed_date.strftime('%Y-%m-%d %H:%M:%S UTC')})")

                            except (ValueError, TypeError) as e:
                                print(f"⚠️  Could not parse date for article '{article.get('title', 'Unknown')}': {e}")
                                # Include articles with unparseable dates as fallback
                                blog_posts.append(article)
                        else:
                            # Include articles without dates as fallback
                            blog_posts.append(article)

                    print(f"📥 Processed {len(articles)} articles ({recent_found_in_batch} recent, Total recent: {len(blog_posts)})")

                    # If no recent articles found in this batch, we can stop (articles are sorted by date)
                    if recent_found_in_batch == 0 and len(articles) > 0:
                        print(f"⏹️  No recent articles in this batch. Stopping fetch.")
                        return blog_posts  # Return immediately instead of just breaking

                    # Check for pagination
                    link_header = response.headers.get('Link', '')
                    if 'rel="next"' in link_header:
                        next_link = [link for link in link_header.split(',') if 'rel="next"' in link][0]
                        page_info = next_link.split('page_info=')[1].split('&')[0].split('>')[0]
                    else:
                        break

                    break  # Break out of retry loop on success
                else:
                    print(f"❌ Failed to fetch articles: {response.status_code} - {response.text}")
                    if attempt < max_retries - 1:
                        time.sleep(2)
            except Exception as e:
                print(f"Error on attempt {attempt+1}/{max_retries}: {e}")
                if attempt < max_retries - 1:
                    time.sleep(2)
        else:
            print(f"❌ Failed to fetch articles after {max_retries} attempts")
            break

        time.sleep(1)

    print(f"🆕 Recent blog posts found: {len(blog_posts)}")
    return blog_posts

def show_blog_tags_preview(blogs, limit=10):
    """Show current tags for a sample of blogs"""
    print(f"\n📋 Current tags preview (showing first {limit} blogs):")
    print("=" * 80)
    
    for i, blog in enumerate(blogs[:limit]):
        current_tags = blog.get('tags', '')
        if isinstance(current_tags, list):
            current_tags = ', '.join(current_tags)
        
        print(f"{i+1}. {blog['title'][:50]}...")
        print(f"   Current tags: {current_tags}")
        print(f"   Created: {blog.get('created_at', 'Unknown')}")
        print("-" * 80)

def update_blog_tags(article_id, new_tags, max_retries=5):
    """Update tags for a specific blog article"""
    url = f"{store_address}/blogs/{blog_id}/articles/{article_id}.json"
    
    payload = {
        "article": {
            "id": article_id,
            "tags": new_tags
        }
    }
    
    for attempt in range(max_retries):
        try:
            response = requests.put(url, headers=headers, json=payload, auth=(api_key, password), timeout=30)
            
            if response.status_code == 200:
                return True
            else:
                print(f"❌ Failed to update tags for article {article_id}: {response.status_code} - {response.text}")
                if attempt < max_retries - 1:
                    time.sleep(2)
        except Exception as e:
            print(f"Error on attempt {attempt+1}/{max_retries}: {e}")
            if attempt < max_retries - 1:
                time.sleep(2)
    
    return False

def replace_tags_for_blogs(blogs, correct_tags, num_tags_per_blog=10):
    """Replace tags for all provided blogs"""
    if not correct_tags:
        print("❌ No correct tags available. Cannot proceed.")
        return
    
    print(f"\n🔄 Starting tag replacement for {len(blogs)} blogs...")
    print(f"📝 Using {num_tags_per_blog} random tags per blog from {len(correct_tags)} available tags")
    
    success_count = 0
    failed_count = 0
    
    for i, blog in enumerate(tqdm(blogs, desc="Updating blog tags")):
        article_id = blog['id']
        title = blog['title']
        
        # Select random tags for this blog
        selected_tags = random.sample(correct_tags, min(num_tags_per_blog, len(correct_tags)))
        
        print(f"\n📝 [{i+1}/{len(blogs)}] Updating: {title[:50]}...")
        print(f"   New tags: {', '.join(selected_tags[:3])}...")
        
        if update_blog_tags(article_id, selected_tags):
            success_count += 1
            print(f"   ✅ Success!")
        else:
            failed_count += 1
            print(f"   ❌ Failed!")
        
        # Small delay to avoid rate limiting
        time.sleep(1)
    
    print(f"\n🎉 Tag replacement completed!")
    print(f"✅ Successfully updated: {success_count}")
    print(f"❌ Failed: {failed_count}")
    print(f"📊 Success rate: {(success_count/len(blogs)*100):.1f}%")

def main():
    print("🏷️  BLOG TAGS REPLACEMENT TOOL")
    print("=" * 50)
    print("Fix tag mismatches by replacing blog tags with correct ones from tags.csv")
    
    # Load correct tags first
    correct_tags = load_correct_tags()
    if not correct_tags:
        print("❌ Cannot proceed without correct tags. Exiting.")
        return
    
    # Show menu options
    print("\n📋 Choose blogs to update:")
    print("1. 🌐 All blogs (may take a while)")
    print("2. 🕒 Recent blogs (last 24 hours)")
    print("3. ⏰ Custom time range (specify hours)")
    print("4. ❌ Exit")
    
    try:
        choice = input("\n🔢 Enter your choice (1-4): ").strip()
        
        blogs = []
        
        if choice == "1":
            print("\n🌐 Fetching ALL blogs...")
            blogs = fetch_all_blogs()
        
        elif choice == "2":
            print("\n🕒 Fetching blogs from last 24 hours...")
            blogs = fetch_recent_blogs(24)
        
        elif choice == "3":
            try:
                hours = int(input("⏰ Enter hours to look back (e.g., 1, 5, 12, 48): ").strip())
                if hours <= 0:
                    print("❌ Hours must be positive!")
                    return
                print(f"\n⏰ Fetching blogs from last {hours} hours...")
                blogs = fetch_recent_blogs(hours)
            except ValueError:
                print("❌ Invalid input! Please enter a number.")
                return
        
        elif choice == "4":
            print("👋 Goodbye!")
            return
        
        else:
            print("❌ Invalid choice!")
            return
        
        if not blogs:
            print("📭 No blogs found to update.")
            return
        
        # Show preview of current tags
        show_blog_tags_preview(blogs)
        
        # Ask for confirmation
        print(f"\n❓ Found {len(blogs)} blogs to update.")
        print(f"🏷️  Each blog will get {min(10, len(correct_tags))} random tags from your tags.csv")
        
        confirm = input("Continue with tag replacement? (y/n): ").strip().lower()
        if confirm != 'y':
            print("❌ Operation cancelled.")
            return
        
        # Ask for number of tags per blog
        try:
            num_tags = int(input(f"🔢 How many tags per blog? (1-{len(correct_tags)}, default: 10): ").strip() or "10")
            num_tags = max(1, min(num_tags, len(correct_tags)))
        except ValueError:
            num_tags = 10
            print("Using default: 10 tags per blog")
        
        # Replace tags
        replace_tags_for_blogs(blogs, correct_tags, num_tags)
        
    except KeyboardInterrupt:
        print("\n❌ Operation cancelled by user")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
