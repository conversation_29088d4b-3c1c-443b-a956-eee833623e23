import g4f
import pandas as pd
import requests
from dotenv import load_dotenv
import os
import traceback
import threading
import json
import random
import time
from tqdm import tqdm
from concurrent.futures import ThreadPoolExecutor, as_completed
from tenacity import retry, stop_after_attempt, wait_random_exponential

# Load the JSON config file
def load_config():
    with open('../config.json', 'r') as config_file:
        return json.load(config_file)

config = load_config()

# Shopify API credentials
api_key = config['shopify']['api_key']
password = config['shopify']['password']
store_address = config['shopify']['store_address']
blog_id = config['shopify']['blog_id']
author = config['shopify']['author']
product_link = config['shopify']['product_link']
product_name = config['shopify']['product_name']

headers = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
}

def load_tags():
    try:
        tags_df = pd.read_csv(config['csv']['tags_csv'])
        tags = tags_df['Tags'].tolist()
        return tags
    except Exception as e:
        print(f"❌ Error loading tags: {e}")
        return []

tags_list = load_tags()

@retry(wait=wait_random_exponential(min=1, max=10), stop=stop_after_attempt(5))
def create_shopify_post(payload):
    url = f'{store_address}/blogs/{blog_id}/articles.json'
    response = requests.post(url, headers=headers, data=json.dumps(payload), auth=(api_key, password))
    if response.status_code == 201:
        print("✅ Blog posted to Shopify.")
    else:
        print(f"❌ Failed to post to Shopify: {response.status_code} - {response.text}")
        response.raise_for_status()

@retry(wait=wait_random_exponential(min=1, max=10), stop=stop_after_attempt(5))
def generate_chat_completion(messages):
    try:
        response = g4f.ChatCompletion.create(
            model='gpt-4o',
            messages=messages,
            provider=g4f.Provider.PollinationsAI,
            timeout=60
        )
        if response and len(str(response).strip()) > 10:
            return response
    except Exception as e:
        print(f"❌ PollinationsAI gpt-4o failed: {str(e)[:50]}...")

    # Fallback to auto-selection
    try:
        response = g4f.ChatCompletion.create(
            model='gpt-4o',
            messages=messages,
            timeout=60
        )
        return response
    except Exception as e:
        print(f"❌ Auto-selection failed: {str(e)[:50]}...")
        return None

def generate_blog_post(row):
    try:
        url_slug = row['URL Slug']
        meta_title = row['Meta Title']
        description = row['Description of Page']

        selected_tags = random.sample(tags_list, 10)

        print(f"🧠 Generating outline for: {url_slug}")
        outline = generate_chat_completion([
            {"role": "system", "content": "You are an essay-writing assistant. Write an outline with at least 15 structured points, including headings and subheadings."},
            {"role": "user", "content": f"Create an outline for an article about {meta_title}."}
        ])

        if not outline:
            print(f"❌ Outline generation failed for {url_slug}")
            return

        print(f"📝 Generating content for: {url_slug}")
        blog_content = generate_chat_completion([
            {"role": "system", "content": f'''
                Write an SEO-optimized blog article using this outline: {outline}.
                Ensure the content is relevant to the title and the topic, structured correctly in HTML format.
                Avoid any metadata or placeholder text, such as the introduction, closing comments, or unrelated content.
                Use proper HTML tags:
                - Use <h1> for the title.
                - Use <h2> for each major section and <h3> for sub-sections.
                - Include a Table of Contents (TOC) with jump links to each <h2> and <h3>.
                - End with a clear Call-to-Action: "Check out this amazing product: <a href='{product_link}'>{product_name}</a>."
            '''},
            {"role": "user", "content": f'''
                Write a complete, engaging, and SEO-friendly blog post based on this outline.
                Follow the structure strictly with no extra metadata, titles, descriptions, or introductory texts.
                Ensure the content is clearly formatted with SEO best practices and HTML tags.
                Make sure the article is relevant to the title and topic, with proper headings.
                Do not include any extra HTML tags at the beginning or end of the content.
            '''}
        ])

        if not blog_content:
            print(f"❌ Blog content generation failed for {url_slug}")
            return

        result = {
            'URL Slug': url_slug,
            'Meta Title': meta_title,
            'Description': description,
            'Blog Content': blog_content,
            'Tags': ", ".join(selected_tags)
        }

        payload = {
            "article": {
                "title": meta_title,
                "author": author,
                "tags": selected_tags,
                "body_html": blog_content
            }
        }
        create_shopify_post(payload)

        # Wait 10 seconds before processing next blog to avoid rate limiting
        print("⏳ Waiting 10 seconds before next blog creation...")
        time.sleep(10)

    except Exception as e:
        print(f"🔥 Error in generating post for {url_slug}: {e}")
        traceback.print_exc()

def main():
    df = pd.read_csv(config['csv']['input_csv'], skipinitialspace=True, engine='python')

    with ThreadPoolExecutor(max_workers=1) as executor:
        futures = [executor.submit(generate_blog_post, row) for _, row in df.iterrows()]
        for _ in tqdm(as_completed(futures), total=len(futures)):
            pass

if __name__ == "__main__":
    main()
