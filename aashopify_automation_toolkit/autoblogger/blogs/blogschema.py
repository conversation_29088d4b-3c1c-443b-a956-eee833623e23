import ollama
import pandas as pd
import requests
import json
import random
import traceback
import time
from tqdm import tqdm
from tenacity import retry, stop_after_attempt, wait_random_exponential

# Load the JSON config file
def load_config():
    with open('../config.json', 'r') as config_file:
        return json.load(config_file)

config = load_config()

# Shopify API credentials
brandname = config['shopify']['brandname']
api_key = config['shopify']['api_key']
password = config['shopify']['password']
store_address = config['shopify']['store_address']
blog_id = config['shopify']['blog_id']
author = config['shopify']['author']
product_link = config['shopify']['product_link']
product_name = config['shopify']['product_name']

headers = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
}

def load_tags():
    try:
        tags_df = pd.read_csv(config['csv']['tags_csv'])
        return tags_df['Tags'].tolist()
    except Exception as e:
        print(f"❌ Error loading tags: {e}")
        return []

tags_list = load_tags()

@retry(wait=wait_random_exponential(min=1, max=10), stop=stop_after_attempt(5))
def create_shopify_post(payload):
    url = f'{store_address}/blogs/{blog_id}/articles.json'
    response = requests.post(
        url,
        headers=headers,
        data=json.dumps(payload),
        auth=(api_key, password)
    )
    if response.status_code == 201:
        print("✅ Blog posted to Shopify.")
    else:
        print(f"❌ Failed to post to Shopify: {response.status_code} - {response.text}")
        response.raise_for_status()

# Function to handle chat completion with Ollama/Llama3
def generate_chat_completion(messages):
    # Combine system and user messages into a single prompt
    prompt = ""
    for m in messages:
        if m["role"] == "system":
            prompt += f"System: {m['content']}\n"
        elif m["role"] == "user":
            prompt += f"User: {m['content']}\n"
    response = ollama.generate(
        model='llama3',  # or 'llama3.1', 'llama4' if available
        prompt=prompt
    )
    return response['response']

def generate_faqs(meta_title):
    try:
        prompt = (
            f"Generate at least 5 frequently asked questions (FAQ) and their answers "
            f"related to the following topic: {meta_title}.\n\n"
            "For each FAQ:\n"
            "- The question should be followed by the answer.\n"
            "- Format the output like this:\n"
            "  Q: [Question]\n"
            "  A: [Answer]\n"
            "Provide the questions and answers in a clear format."
        )
        faq_response = generate_chat_completion([
            {"role": "system", "content": "You are an expert content creator."},
            {"role": "user", "content": prompt}
        ])

        if not faq_response:
            print(f"❌ FAQ generation failed for {meta_title}")
            return []

        faq_content = faq_response
        faqs = []
        question = None
        answer = None
        for line in faq_content.splitlines():
            line = line.strip()
            if line.lower().startswith("q:"):
                if question and answer:
                    faqs.append({
                        "@type": "Question",
                        "name": question,
                        "acceptedAnswer": {
                            "@type": "Answer",
                            "text": answer
                        }
                    })
                question = line[2:].strip()
                answer = None
            elif line.lower().startswith("a:") and question:
                answer = line[2:].strip()
        if question and answer:
            faqs.append({
                "@type": "Question",
                "name": question,
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": answer
                }
            })
        return faqs[:5]
    except Exception as e:
        print(f"🔥 Error generating FAQs: {e}")
        return []

def generate_blog_post(row):
    try:
        url_slug = row['URL Slug']
        meta_title = row['Meta Title']
        description = row['Description of Page']
        author_name = author
        page_url = f"{store_address}/blogs/{blog_id}/{url_slug}"
        date_published = "2025-04-28"
        selected_tags = random.sample(tags_list, min(10, len(tags_list)))

        print(f"🧠 Generating outline for: {url_slug}")
        outline = generate_chat_completion([
            {"role": "system", "content": "You are an essay-writing assistant. Write an outline with at least 15 structured points, including headings and subheadings."},
            {"role": "user", "content": f"Create an outline for an article about {meta_title}."}
        ])
        if not outline:
            print(f"❌ Outline generation failed for {url_slug}")
            return

        print(f"📝 Generating content for: {url_slug}")
        blog_content = generate_chat_completion([
            {"role": "system", "content": f'''
                Write an SEO-optimized blog article using this outline: {outline}.
                Ensure the content is relevant to the title and the topic, structured correctly in HTML format.
                Avoid any metadata or placeholder text, such as the introduction, closing comments, or unrelated content.
                Use proper HTML tags:
                - Use <h1> for the title.
                - Use <h2> for each major section and <h3> for sub-sections.
                - Include a Table of Contents (TOC) with jump links to each <h2> and <h3>.
                - End with a clear Call-to-Action: "Check out this amazing product: <a href='{product_link}'>{product_name}</a>."
            '''},
            {"role": "user", "content": f'''
                Write a complete, engaging, and SEO-friendly blog post based on this outline.
                Follow the structure strictly with no extra metadata, titles, descriptions, or introductory texts.
                Ensure the content is clearly formatted with SEO best practices and HTML tags.
                Make sure the article is relevant to the title and topic, with proper headings.
                Do not include any extra HTML tags at the beginning or end of the content.
            '''}
        ])
        if not blog_content:
            print(f"❌ Blog content generation failed for {url_slug}")
            return

        faqs = generate_faqs(meta_title)
        faq_schema = {"@type": "FAQPage", "mainEntity": faqs}

        schema_jsonld = json.dumps({
            "@context": "https://schema.org",
            "@type": "Article",
            "headline": meta_title,
            "description": description,
            "author": {"@type": "Person", "name": author_name},
            "publisher": {"@type": "Organization", "name": brandname},
            "mainEntityOfPage": page_url,
            "datePublished": date_published,
            "dateModified": date_published
        }, indent=2)

        faq_jsonld = json.dumps(faq_schema, indent=2)
        body = (
            blog_content
            + "\n<script type=\"application/ld+json\">\n"
            + schema_jsonld
            + "\n</script>\n"
            + "<script type=\"application/ld+json\">\n"
            + faq_jsonld
            + "\n</script>"
        )

        payload = {
            "article": {
                "title": meta_title,
                "author": author,
                "tags": selected_tags,
                "body_html": body
            }
        }
        create_shopify_post(payload)

        # Wait 10 seconds before processing next blog to avoid rate limiting
        print("⏳ Waiting 10 seconds before next blog creation...")
        time.sleep(10)

    except Exception as e:
        print(f"🔥 Error in generating post for {url_slug}: {e}")
        traceback.print_exc()

def main():
    df = pd.read_csv(config['csv']['input_csv'], skipinitialspace=True, engine='python')
    for _, row in df.iterrows():
        generate_blog_post(row)

if __name__ == "__main__":
    main()